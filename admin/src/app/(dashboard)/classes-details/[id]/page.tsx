"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { axiosInstance } from "@/lib/axios";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { IoShieldCheckmark } from "react-icons/io5";
import { FaAddressBook, FaGoogleScholar } from "react-icons/fa6";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { format } from "date-fns";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { BsPersonCircle } from "react-icons/bs";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";
import { sendMail } from "@/services/email";
import { FaPiggyBank } from "react-icons/fa";

const TABS = [
  { key: "profile", label: "Profile", icon: <BsPersonCircle /> },
  { key: "education", label: "Education", icon: <FaGoogleScholar /> },
  { key: "work", label: "Work Experience", icon: <IoShieldCheckmark /> },
  {
    key: "certifications",
    label: "Certifications",
    icon: <IoShieldCheckmark />,
  },
  { key: "tuition", label: "Tuition Classes", icon: <FaGoogleScholar /> },
  { key: "address", label: "Address", icon: <FaAddressBook /> },
  { key: "bankPayment", label: "Bank Details", icon: <FaPiggyBank /> },
];

const AdminReviewPage = () => {
  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("profile");
  const [bankPaymentData, setBankPaymentData] = useState<any>(null);
  const [bankPaymentLoading, setBankPaymentLoading] = useState(false);
  const [addressData, setAddressData] = useState<any>(null);
  const [addressLoading, setAddressLoading] = useState(false);
  const params = useParams();
  const userId = params.id;

  // for Send Mail functionality
  const [formdata, setFormData] = useState({
    email: "",
    subject: "",
  });
  const [value, setValue] = useState("");
  const [loading, setLoading] = useState(false);

  const fetchTeacher = async () => {
    try {
      setIsLoading(true);
      const res = await axiosInstance.get(`classes/details/${userId}/admin`);
      setData(res.data);
    } catch (err) {
      console.error("Failed to fetch teacher data", err);
      toast.error("Failed to load teacher data");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBankPaymentDetails = async () => {
    try {
      setBankPaymentLoading(true);
      const res = await axiosInstance.get(`bank-payment/details`);
      if (res.data.success) {
        setBankPaymentData(res.data.data);
      } else {
        setBankPaymentData(null);
      }
    } catch (err) {
      console.error("Failed to fetch bank payment details", err);
      setBankPaymentData(null);
    } finally {
      setBankPaymentLoading(false);
    }
  };

  const fetchAddressDetails = async () => {
    try {
      setAddressLoading(true);
      const res = await axiosInstance.get(`classes-profile/admin/${userId}/address`);
      if (res.data.success) {
        setAddressData(res.data.data);
      } else {
        setAddressData(null);
      }
    } catch (err) {
      console.error("Failed to fetch address details", err);
      setAddressData(null);
    } finally {
      setAddressLoading(false);
    }
  };

  useEffect(() => {
    fetchTeacher();
    fetchBankPaymentDetails();
    fetchAddressDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  const handleStatusChange = async (newStatus: string) => {
    try {
      await axiosInstance.patch(`classes/status/${userId}`, {
        status: newStatus,
      });

      toast.success(`Status updated to ${newStatus} & Mail Send`);
      fetchTeacher();
    } catch (err) {
      toast.error("Failed to update status");
      console.error("Failed to update status", err);
    }
  };

  const handleRecordStatusUpdate = async (
    recordId: string,
    recordType: "experience" | "education" | "certificate",
    newStatus: string
  ) => {
    try {
      await axiosInstance.patch(
        `classes-profile/admin/${recordType}/${recordId}/status`,
        {
          status: newStatus,
        }
      );

      toast.success(`${recordType} status updated to ${newStatus}`);
      fetchTeacher();
    } catch (err) {
      toast.error(`Failed to update ${recordType} status`);
      console.error(`Failed to update ${recordType} status`, err);
    }
  };

  const parseAndJoinArray = (value: any): string => {
    try {
      const parsed = typeof value === "string" ? JSON.parse(value) : value;
      return Array.isArray(parsed) ? parsed.join(", ") : parsed || "N/A";
    } catch {
      return value || "N/A";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10 text-gray-600 dark:text-gray-400">
        No data available
      </div>
    );
  }

  const {
    firstName = "",
    lastName = "",
    education = [],
    experience = [],
    certificates = [],
    ClassAbout = {},
    id = "",
    status = { status: "PENDING" },
    tuitionClasses = [],
  } = data;

  const fullName = `${firstName} ${lastName}`.trim() || "Unnamed";
  const profileImg = ClassAbout?.profilePhoto
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`
    : "/teacher-profile.jpg";

  const logoImg = ClassAbout?.classesLogo
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.classesLogo}`
    : "/teacher-profile.jpg";

  const statusColors: Record<string, string> = {
    APPROVED:
      "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200",
    PENDING:
      "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200",
    REJECTED: "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-200",
  };

  // Status Badge Component
  const StatusBadge = ({ status }: { status: string }) => (
    <Badge
      className={
        statusColors[status] ||
        "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
      }
    >
      {status}
    </Badge>
  );

  // Status Action Buttons Component
  const StatusActionButtons = ({
    recordId,
    recordType,
    currentStatus,
  }: {
    recordId: string;
    recordType: "experience" | "education" | "certificate";
    currentStatus: string;
  }) => (
    <div className="flex gap-2 mt-2">
      <Button
        size="sm"
        className="bg-green-500 hover:bg-green-600 text-white"
        onClick={() =>
          handleRecordStatusUpdate(recordId, recordType, "APPROVED")
        }
        disabled={currentStatus === "APPROVED"}
      >
        Approve
      </Button>
      <Button
        size="sm"
        className="bg-red-500 hover:bg-red-600 text-white"
        onClick={() =>
          handleRecordStatusUpdate(recordId, recordType, "REJECTED")
        }
        disabled={currentStatus === "REJECTED"}
      >
        Reject
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={() =>
          handleRecordStatusUpdate(recordId, recordType, "PENDING")
        }
        disabled={currentStatus === "PENDING"}
      >
        Reset
      </Button>
    </div>
  );

  //  Email functionality
  const handleChange = (e: any) => {
    setFormData((prev) => ({
      ...prev,
      email: data.email,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await sendMail({
        email: formdata.email,
        subject: formdata.subject,
        message: value,
      });
      toast.success("Mail sent successfully!");
      setFormData({
        email: "",
        subject: "",
      });
      setValue("");
    } catch (err: any) {
      toast.error(err.message || "Failed to send mail.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12 bg-gray-50 dark:bg-gray-900">
        <section className="grid md:grid-cols-4 gap-8">
          {/* Profile Section */}
          <div className="md:col-span-3 space-y-8">
            {/* Profile Header */}
            <div className="flex flex-col sm:flex-row gap-6 bg-gradient-to-r from-orange-50 to-white dark:from-gray-800 dark:to-gray-900 p-6 rounded-2xl shadow-sm border">
              <div className="relative w-full sm:w-64 h-64 rounded-xl overflow-hidden shadow-lg">
                <Image
                  src={logoImg}
                  alt={`${fullName}'s profile photo`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 256px"
                />
              </div>
              <div className="flex-1 space-y-4">
                <div className="flex items-center gap-3">
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    {fullName}
                  </h1>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-2">
                          <IoShieldCheckmark
                            className={`text-xl ${
                              status && status.status === "APPROVED"
                                ? "text-green-500"
                                : "text-gray-400 dark:text-gray-500"
                            }`}
                          />
                          <Badge
                            className={
                              statusColors[status && status.status] ||
                              "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
                            }
                          >
                            {status && status.status.toUpperCase()}
                          </Badge>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        {status &&
                        status.status === "APPROVED" &&
                        status.createdAt
                          ? `Verified on ${format(
                              new Date(status.createdAt),
                              "PPP"
                            )}`
                          : "Verification pending"}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <p className="text-lg font-medium text-gray-600 dark:text-gray-300">
                  {ClassAbout?.catchyHeadline || "Professional Educator"}
                </p>
                <p className="text-sm w-96 break-words  text-gray-600 dark:text-gray-300">
                  {ClassAbout?.tutorBio || "No bio available"}
                </p>
              </div>
            </div>

            {/* Resume Section */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
                Resume
              </h2>
              {/* Tabs */}
              <div className="flex flex-wrap gap-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                {TABS.map(({ key, label, icon }) => (
                  <button
                    key={key}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                      activeTab === key
                        ? "bg-orange-100 text-customOrange dark:bg-orange-900 dark:text-orange-200 font-semibold"
                        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => setActiveTab(key)}
                  >
                    {icon}
                    {label}
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              <div className="space-y-4">
                {activeTab === "profile" && (
                  <div className="grid gap-4">
                    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        User Name: {data.username}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-white">
                        Full Name: {firstName || "N/A"} {lastName || ""}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Email: {data.email || "N/A"}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Phone: {data.contactNo || "N/A"}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Birth Date:{" "}
                        {ClassAbout?.birthDate
                          ? format(new Date(ClassAbout.birthDate), "dd-MM-yyyy")
                          : "N/A"}
                      </p>
                    </div>
                  </div>
                )}

                {activeTab === "education" && (
                  <div className="grid gap-4">
                    {education.length ? (
                      education.map(
                        (edu: any, idx: number) =>
                          edu.isDegree ? (
                            <div
                              key={idx}
                              className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border"
                            >
                              <div className="flex justify-between items-start mb-2">
                                <p className="font-semibold text-gray-900 dark:text-white">
                                  {edu.university || "Unknown University"}
                                </p>
                                <StatusBadge status={edu.status || "PENDING"} />
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {edu.degree || "N/A"} —{" "}
                                {edu.degreeType || "N/A"}
                              </p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                Passout Year: {edu.passoutYear || "N/A"}
                              </p>
                              {edu.certificate && (
                                <a
                                  href={`${process.env.NEXT_PUBLIC_API_BASE_URL}uploads/classes/${id}/education/${edu.certificate}`}
                                  download
                                  className="text-customOrange dark:text-orange-400 hover:underline text-sm block mt-2"
                                >
                                  Download Certificate
                                </a>
                              )}
                              <StatusActionButtons
                                recordId={edu.id}
                                recordType="education"
                                currentStatus={edu.status || "PENDING"}
                              />
                            </div>
                          ) : (
                            "No education details available"
                          ) // ⬅️ Don't forget this
                      )
                    ) : (
                      <p className="text-gray-600 dark:text-gray-300">
                        No education details available
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "work" && (
                  <div className="grid gap-4">
                    {experience.filter((exp: any) => exp.isExperience)
                      .length ? (
                      experience
                        .filter((exp: any) => exp.isExperience)
                        .map((exp: any, idx: number) => (
                          <div
                            key={idx}
                            className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border"
                          >
                            <div className="flex justify-between items-start mb-2">
                              <p className="font-semibold text-gray-900 dark:text-white">
                                {exp.title || "Job Title"}
                              </p>
                              <StatusBadge status={exp.status || "PENDING"} />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              From:{" "}
                              {exp.from
                                ? format(new Date(exp.from), "MMM yyyy")
                                : "N/A"}{" "}
                              — To:{" "}
                              {exp.to
                                ? format(new Date(exp.to), "MMM yyyy")
                                : "Present"}
                            </p>
                            {exp.certificateUrl && (
                              <a
                                href={`${process.env.NEXT_PUBLIC_API_BASE_URL}uploads/classes/${id}/experience/${exp.certificateUrl}`}
                                download
                                className="text-orange-600 dark:text-orange-400 hover:underline text-sm block mt-2"
                              >
                                Download Experience Certificate
                              </a>
                            )}
                            <StatusActionButtons
                              recordId={exp.id}
                              recordType="experience"
                              currentStatus={exp.status || "PENDING"}
                            />
                          </div>
                        ))
                    ) : (
                      <p className="text-gray-600 dark:text-gray-300">
                        No work experience available
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "certifications" && (
                  <div className="grid gap-4">
                    {certificates.filter((cert: any) => cert.isCertificate)
                      .length ? (
                      certificates
                        .filter((cert: any) => cert.isCertificate)
                        .map((cert: any, idx: number) => (
                          <div
                            key={idx}
                            className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border"
                          >
                            <div className="flex justify-between items-start mb-2">
                              <p className="font-semibold text-gray-900 dark:text-white">
                                {cert.title || "Certificate Title"}
                              </p>
                              <StatusBadge status={cert.status || "PENDING"} />
                            </div>
                            {cert.certificateUrl && (
                              <a
                                href={`${process.env.NEXT_PUBLIC_API_BASE_URL}uploads/classes/${id}/certificates/${cert.certificateUrl}`}
                                download
                                className="text-customOrange dark:text-orange-400 hover:underline text-sm block mt-2"
                              >
                                Download Certificate
                              </a>
                            )}
                            <StatusActionButtons
                              recordId={cert.id}
                              recordType="certificate"
                              currentStatus={cert.status || "PENDING"}
                            />
                          </div>
                        ))
                    ) : (
                      <p className="text-gray-600 dark:text-gray-300">
                        No certifications available
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "tuition" && (
                  <div className="grid gap-4">
                    {tuitionClasses.length ? (
                      tuitionClasses.map((tuition: any, idx: number) => (
                        <div
                          key={idx}
                          className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border"
                        >
                          <p className="text-lg font-semibold text-gray-900 dark:text-white">
                            Tuition #{idx + 1}
                          </p>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 text-sm text-gray-600 dark:text-gray-300">
                            <div>
                              <strong>Category:</strong>{" "}
                              {tuition.education || "N/A"}
                            </div>
                            <div>
                              <strong>Coaching Type:</strong>{" "}
                              {parseAndJoinArray(tuition.coachingType)}
                            </div>
                            {tuition.education === "Education" ? (
                              <>
                                <div>
                                  <strong>Board:</strong>{" "}
                                  {parseAndJoinArray(tuition.boardType)}
                                </div>
                                <div>
                                  <strong>Medium:</strong>{" "}
                                  {parseAndJoinArray(tuition.medium)}
                                </div>
                                <div>
                                  <strong>Section:</strong>{" "}
                                  {parseAndJoinArray(tuition.section)}
                                </div>
                                <div>
                                  <strong>Subject:</strong>{" "}
                                  {parseAndJoinArray(tuition.subject)}
                                </div>
                              </>
                            ) : (
                              <div>
                                <strong>Details:</strong>{" "}
                                {parseAndJoinArray(tuition.details)}
                              </div>
                            )}
                            <div>
                              <strong>Monthly Fee:</strong> ₹
                              {tuition.pricingPerMonth || "0"}
                            </div>
                            <div className="sm:col-span-2">
                              <strong>Pricing Per Course:</strong> ₹
                              {tuition.pricingPerCourse || "0"}
                            </div>
                            {tuition.timeSlots?.length > 0 && (
                              <div className="sm:col-span-2">
                                <p className="font-medium">Time Slots:</p>
                                <ul className="list-disc ml-6 mt-1 space-y-1">
                                  {tuition.timeSlots.map(
                                    (slot: any, i: number) => (
                                      <li key={i}>
                                        {slot.from} — {slot.to}
                                      </li>
                                    )
                                  )}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-600 dark:text-gray-300">
                        No tuition classes listed yet
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "address" && (
                  <div className="grid gap-4">
                    {addressLoading ? (
                      <div className="flex justify-center items-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-orange-500" />
                        <span className="ml-2 text-gray-600 dark:text-gray-300">Loading address details...</span>
                      </div>
                    ) : addressData ? (
                      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                          Address Details
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300">
                          <div className="sm:col-span-2">
                            <strong>Full Address:</strong>
                            <p className="mt-1 p-2 bg-gray-50 dark:bg-gray-700 rounded">{addressData.fullAddress || "N/A"}</p>
                          </div>
                          <div>
                            <strong>City:</strong> {addressData.city || "N/A"}
                          </div>
                          <div>
                            <strong>State:</strong> {addressData.state || "N/A"}
                          </div>
                          <div>
                            <strong>Postcode:</strong> {addressData.postcode || "N/A"}
                          </div>
                          <div>
                            <strong>Country:</strong> {addressData.country || "N/A"}
                          </div>
                          <div>
                            <strong>Latitude:</strong> {addressData.latitude || "N/A"}
                          </div>
                          <div>
                            <strong>Longitude:</strong> {addressData.longitude || "N/A"}
                          </div>
                          <div className="sm:col-span-2">
                            <strong>Created At:</strong>{" "}
                            {addressData.createdAt
                              ? format(new Date(addressData.createdAt), "PPP")
                              : "N/A"}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-600 dark:text-gray-300">
                        No address details available
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "bankPayment" && (
                  <div className="grid gap-4">
                    {bankPaymentLoading ? (
                      <div className="flex justify-center items-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-orange-500" />
                        <span className="ml-2 text-gray-600 dark:text-gray-300">Loading bank payment details...</span>
                      </div>
                    ) : bankPaymentData ? (
                      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                          Bank Payment Details
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300">
                          <div>
                            <strong>Bank Name:</strong> {bankPaymentData.bankName || "N/A"}
                          </div>
                          <div>
                            <strong>Account Holder Name:</strong> {bankPaymentData.accountHolderName || "N/A"}
                          </div>
                          <div>
                            <strong>Account Number:</strong> {bankPaymentData.accountNumber || "N/A"}
                          </div>
                          <div>
                            <strong>Re-Account Number:</strong> {bankPaymentData.reAccountNumber || "N/A"}
                          </div>
                          <div>
                            <strong>IFSC Code:</strong> {bankPaymentData.ifscCode || "N/A"}
                          </div>
                          <div>
                            <strong>Branch Name:</strong> {bankPaymentData.branchName || "N/A"}
                          </div>
                          <div>
                            <strong>Created At:</strong>{" "}
                            {bankPaymentData.createdAt
                              ? format(new Date(bankPaymentData.createdAt), "PPP")
                              : "N/A"}
                          </div>
                          <div>
                            <strong>Updated At:</strong>{" "}
                            {bankPaymentData.updatedAt
                              ? format(new Date(bankPaymentData.updatedAt), "PPP")
                              : "N/A"}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-600 dark:text-gray-300">
                        No bank payment details available
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Send Mail  */}
            <div className="p-6 bg-white rounded-xl shadow-lg mt-10">
              <h2 className="text-2xl capitalize font-bold text-gray-800 mb-4 text-center">
                Send <span className="text-orange-400">Mail</span>
              </h2>
              <form className="space-y-4" onSubmit={handleSubmit}>
                <input
                  readOnly
                  type="text"
                  placeholder="Enter mail"
                  name="email"
                  className="w-full p-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-customOrange"
                  value={formdata.email || data.email}
                  onChange={handleChange}
                />

                <input
                  type="text"
                  placeholder="Enter Subject"
                  name="subject"
                  className="w-full p-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400"
                  value={formdata.subject}
                  onChange={handleChange}
                />

                <ReactQuill
                  theme="snow"
                  value={value}
                  onChange={setValue}
                  style={{ height: "150px" }}
                  className="bg-white rounded-lg mb-16"
                />

                <button className="w-full bg-orange-400 hover:bg-orange-500 text-white font-medium py-3 rounded-lg transition duration-200">
                  {" "}
                  {loading ? "Sending..." : "Send Mail"}
                </button>
              </form>
            </div>
          </div>

          {/* Sidebar */}
          <aside className="sticky top-24 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border space-y-6">
            <div className="relative w-full h-48 rounded-xl overflow-hidden">
              <Image
                src={profileImg}
                alt={`${fullName}'s profile photo`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 192px"
              />
            </div>
            <div className="space-y-3">
              <Button
                className="w-full bg-green-500 hover:bg-green-600 text-white transition-colors cursor-pointer"
                onClick={() => handleStatusChange("APPROVED")}
                disabled={status && status.status === "APPROVED"}
              >
                Approved Profile
              </Button>
              <Button
                className="w-full bg-red-500 hover:bg-red-600 text-white transition-colors cursor-pointer"
                onClick={() => handleStatusChange("REJECTED")}
                disabled={status && status.status === "REJECTED"}
              >
                Reject Profile
              </Button>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Status: {status && status.status.toUpperCase()}
              </p>
              {status && status.status === "APPROVED" && status.createdAt && (
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Verified on{" "}
                  {format(new Date(status.createdAt), "MMM d, yyyy")}
                </p>
              )}
            </div>
          </aside>
        </section>
      </main>
    </>
  );
};

export default AdminReviewPage;
