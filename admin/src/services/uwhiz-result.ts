import axiosInstance from '../lib/axios';

interface ExamRankingFilters {
  firstName?: string;
  lastName?: string;
  email?: string;
  score?: string;
  rank?: string;
  contact?: string;
}

export const getExamRankings = async (
  examId: string | number,
  page: number = 1,
  limit: number = 10,
  filters: ExamRankingFilters = {}
) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    // Only add non-empty filters
    if (filters.firstName?.trim()) params.append('firstName', filters.firstName.trim());
    if (filters.lastName?.trim()) params.append('lastName', filters.lastName.trim());
    if (filters.email?.trim()) params.append('email', filters.email.trim());
    if (filters.score?.trim()) params.append('score', filters.score.trim());
    if (filters.contact?.trim()) params.append('contact', filters.contact.trim());
    if (filters.rank?.trim()) params.append('rank', filters.rank.trim());


    const response = await axiosInstance.get(
      `/uwhizResult/rankings/${examId}?${params.toString()}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );

    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get exam rankings: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const resetExamRankingFilters = async (
  examId: string | number,
  page: number = 1,
  limit: number = 10
) => {
  return await getExamRankings(examId, page, limit, {});
};
export const downloadExamRankingsExcel = async (
  examId: string | number,
  filters: ExamRankingFilters = {}
) => {
  try {
    const queryParams = new URLSearchParams({
      ...(filters.firstName?.trim() && { firstName: filters.firstName.trim() }),
      ...(filters.lastName?.trim() && { lastName: filters.lastName.trim() }),
      ...(filters.email?.trim() && { email: filters.email.trim() }),
      ...(filters.score?.trim() && { score: filters.score.trim() }),
      ...(filters.rank?.trim() && { rank: filters.rank.trim() }),
      ...(filters.contact?.trim() && {contact : filters.contact.trim() }),
    }).toString();

    const url = `/export/uwhizResult/rankings/${examId}${queryParams ? `?${queryParams}` : ''}`;

    const response = await axiosInstance.get(url, {
      headers: {
        "Server-Select": "uwhizServer",
      },
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `exam-rankings-${examId}.xlsx`);
    document.body.appendChild(link);
    link.click();

    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return true;
  } catch (error: any) {
    throw new Error(`Failed to download Excel file: ${error.message}`);
  }
};

export type { ExamRankingFilters };