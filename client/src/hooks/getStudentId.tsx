'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { axiosInstance } from '@/lib/axios';

const useStudentId = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [studentId, setStudentId] = useState<string | null>(null);

  useEffect(() => {
    const init = async () => {
      const token = searchParams.get('token');

      if (token) {
        try {
          const response = await axiosInstance.get(`/student/login-with-jwt`, {
            params: { token },
            withCredentials: true,
          });

          const data = response.data.data;

         if (data?.userId) {
            const studentData = {
              id: data.userId,
              contactNo: data.contactNo,
              firstName: data.firstName,
              lastName: data.lastName,
            };

            localStorage.setItem('student_data', JSON.stringify(studentData));
            localStorage.setItem('studentToken', token);
            localStorage.setItem('mobile_request', "true");

            setStudentId(data.userId);
          }

          const newUrl = window.location.pathname;
          router.replace(newUrl);
        } catch (error) {
          console.error('JWT login failed:', error);
        }
      } else {
        const local = localStorage.getItem('student_data');
        const parsed = local ? JSON.parse(local) : null;
        setStudentId(parsed?.id || null);
      }
    };

    init();
  }, [searchParams, router]);

  return studentId;
};

export default useStudentId;