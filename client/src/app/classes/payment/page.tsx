"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Footer from "@/app-components/Footer";
import Header from "@/app-components/Header";
import { Wallet, History, Loader2, ShieldCheck } from "lucide-react";
import { Input } from "@/components/ui/input";
import { axiosInstance } from "@/lib/axios";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Enum for method types
const MethodEnum = z.enum(["BANK", "UPI"]);

const baseSchema = z.object({
  defaultMethod: MethodEnum,
});

const rawBankSchema = baseSchema.extend({
  defaultMethod: z.literal("BANK"),
  bankName: z
    .string()
    .min(2, "Bank name must be at least 2 characters")
    .max(100)
    .regex(/^[A-Za-z\s]+$/, "Only letters and spaces allowed"),
  accountNumber: z
    .string()
    .regex(/^[0-9]{9,18}$/, "Account number must be 9-18 digits"),
  reAccountNumber: z.string(),
  ifscCode: z
    .string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, "Invalid IFSC code format"),
  accountHolderName: z
    .string()
    .min(2)
    .max(100)
    .regex(/^[A-Za-z\s]+$/, "Only letters and spaces allowed"),
  branchName: z.string().min(2).max(100),
  upiId: z.string().optional(),
});

const upiSchema = baseSchema.extend({
  defaultMethod: z.literal("UPI"),
  upiId: z.string().regex(/^[\w.-]{2,}@[\w]{2,}$/, "Invalid UPI ID"),
  bankName: z.string().optional(),
  accountNumber: z.string().optional(),
  reAccountNumber: z.string().optional(),
  ifscCode: z.string().optional(),
  accountHolderName: z.string().optional(),
  branchName: z.string().optional(),
});

const bankPaymentSchema = z
  .discriminatedUnion("defaultMethod", [rawBankSchema, upiSchema])
  .refine(
    (data) => {
      if (data.defaultMethod === "BANK") {
        return data.accountNumber === data.reAccountNumber;
      }
      return true;
    },
    {
      path: ["reAccountNumber"],
      message: "Account numbers do not match",
    }
  );

type BankPaymentFormValues = z.infer<typeof bankPaymentSchema>;

const PaymentPage = () => {
  const [loading, setLoading] = useState(false);
  const [existingPaymentId, setExistingPaymentId] = useState<string | null>(
    null
  );

  const form = useForm<BankPaymentFormValues>({
    resolver: zodResolver(bankPaymentSchema),
    defaultValues: {
      defaultMethod: "BANK",
      bankName: "",
      accountNumber: "",
      reAccountNumber: "",
      ifscCode: "",
      accountHolderName: "",
      branchName: "",
      upiId: "",
    },
    mode: "onChange",
  });

  const selectedMethod = form.watch("defaultMethod");

  useEffect(() => {
    fetchBankPaymentDetails();
  }, []);

  const fetchBankPaymentDetails = async () => {
    try {
      const response = await axiosInstance.get("/bank-payment/details");
      const data = response.data?.data;

      if (response.data.success && data) {
        setExistingPaymentId(data.id);
        form.reset({
          bankName: data.bankName || "",
          accountNumber: data.accountNumber || "",
          reAccountNumber: data.accountNumber || "",
          ifscCode: data.ifscCode || "",
          accountHolderName: data.accountHolderName || "",
          branchName: data.branchName || "",
          upiId: data.upiId || "",
          defaultMethod: data.defaultMethod || "BANK",
        });
      } else {
        setExistingPaymentId(null);
      }
    } catch (error) {
      console.error("Error fetching bank payment details:", error);
      setExistingPaymentId(null);
    }
  };

  const onSubmit = async (data: BankPaymentFormValues) => {
    setLoading(true);
    try {
      const response = existingPaymentId
        ? await axiosInstance.put(
            `/bank-payment/update/${existingPaymentId}`,
            data
          )
        : await axiosInstance.post("/bank-payment/create", data);

      toast.success(
        existingPaymentId
          ? "Bank payment details updated successfully!"
          : "Bank payment details created successfully!"
      );

      if (
        !existingPaymentId &&
        response.data.success &&
        response.data.data?.id
      ) {
        setExistingPaymentId(response.data.data.id);
      }
    } catch (error: any) {
      console.error("Error saving bank payment details:", error);
      toast.error(
        error.response?.data?.message || "Error saving bank payment details"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Header />
      <div className="container mx-auto px-4 md:px-16 py-8 space-y-6">
        <div className="flex items-start gap-3 mb-6">
          <ShieldCheck className="text-green-600 mt-1" size={20} />
          <div>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-white">
              Payment Portal
            </h2>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
              Manage your payments and view transaction history. These details
              are used to credit money to your account.
              <span className="block mt-1 font-medium text-green-700 dark:text-green-400">
                Don’t worry, it is safe and secure.
              </span>
            </p>
          </div>
        </div>

        <Tabs defaultValue="payment" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="payment" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Account Details
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Payment History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5 text-orange-600" />
                  Payment Method
                </CardTitle>
                <CardDescription>
                  Select your preferred payment method and enter details
                  accordingly.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="defaultMethod"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Payment Method *</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select Method" />
                              </SelectTrigger>
                              <SelectContent className="w-full">
                                <SelectItem value="BANK">Bank</SelectItem>
                                <SelectItem value="UPI">UPI</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {selectedMethod === "BANK" && (
                      <div className="grid gap-4 md:grid-cols-2">
                        {[
                          ["bankName", "Bank Name *"],
                          ["accountNumber", "Account Number *"],
                          ["reAccountNumber", "Re-enter Account Number *"],
                          ["ifscCode", "IFSC Code *"],
                          ["accountHolderName", "Account Holder Name *"],
                          ["branchName", "Branch Name *"],
                        ].map(([name, label]) => (
                          <FormField
                            key={name}
                            control={form.control}
                            name={name as keyof BankPaymentFormValues}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{label}</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder={`Enter ${label}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    )}

                    {selectedMethod === "UPI" && (
                      <FormField
                        control={form.control}
                        name="upiId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>UPI ID *</FormLabel>
                            <FormControl>
                              <Input placeholder="example@upi" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <Button
                      type="submit"
                      className="w-full bg-customOrange hover:bg-orange-600 text-white"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {existingPaymentId ? "Updating..." : "Saving..."}
                        </>
                      ) : existingPaymentId ? (
                        "Update Payment Details"
                      ) : (
                        "Submit Payment Details"
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5 text-orange-600" />
                  Payment History
                </CardTitle>
                <CardDescription>
                  View all your payment transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse text-sm">
                    <thead>
                      <tr className="border-b">
                        {[
                          "Date",
                          "Bank Name",
                          "Account Number",
                          "Account Holder",
                          "Status",
                        ].map((heading) => (
                          <th
                            key={heading}
                            className="text-left p-3 font-medium"
                          >
                            {heading}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-3 text-gray-500" colSpan={5}>
                          No payment history found
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      <Footer />
    </>
  );
};

export default PaymentPage;
