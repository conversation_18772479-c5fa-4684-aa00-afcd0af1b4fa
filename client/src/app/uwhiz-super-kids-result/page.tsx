'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Crown } from 'lucide-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import cellularWord from '../../../public/cellular_world.jpeg';
import rbNews from '../../../public/rb-news.png';
import winnerPhoto from '../../../public/uwhiz-kids-winner.jpeg';
import { PiCertificateFill, PiRanking } from 'react-icons/pi';
import { toast } from 'sonner';

interface UwhizKidsResult {
    rank: number;
    firstName: string;
    lastName: string;
    contact: string;
}

interface StudentData {
    id: string;
    contactNo: string;
    firstName: string;
    lastName: string;
    email: string;
}

const getStudentData = (): StudentData | null => {
    try {
        const data = localStorage.getItem("student_data");
        return data ? JSON.parse(data) : null;
    } catch {
        return null;
    }
};

const staticData: UwhizKidsResult[] = [
    { rank: 1, firstName: "Mantra", lastName: "Upadhyay", contact: "7874695695" },
    { rank: 2, firstName: "Vihan", lastName: "Khambhara", contact: "8141979706" },
    { rank: 3, firstName: "Ridham", lastName: "Mayankbhai", contact: "8758772310" },
    { rank: 4, firstName: "Krishn", lastName: "Detroja", contact: "7359977877" },
    { rank: 5, firstName: "Meet", lastName: "Bhalala", contact: "6351796542" },
    { rank: 6, firstName: "Vyom", lastName: "Kothadiya", contact: "9998919873" },
    { rank: 7, firstName: "Rachit", lastName: "Nimavat", contact: "9979225525" },
    { rank: 8, firstName: "Dharvi", lastName: "Kavar", contact: "9998369081" },
    { rank: 9, firstName: "Dhairya", lastName: "Kavar", contact: "7096686246" },
    { rank: 10, firstName: "Hiyan", lastName: "Sanandiya", contact: "9979927027" },
    { rank: 11, firstName: "Daksh", lastName: "Gadara", contact: "9664850389" },
    { rank: 12, firstName: "Janki", lastName: "Sanandiya", contact: "9925188604" },
    { rank: 13, firstName: "Harshil", lastName: "Padsumbiya", contact: "9909919681" },
    { rank: 14, firstName: "Aarav", lastName: "Amrutiya", contact: "9723506065" },
    { rank: 15, firstName: "Dhruvi", lastName: "Detroja", contact: "9909874744" },
    { rank: 16, firstName: "Henil", lastName: "Rokad", contact: "9909401221" },
    { rank: 17, firstName: "Riya", lastName: "Bhatiya", contact: "7990785497" },
    { rank: 18, firstName: "Raghav", lastName: "Padsumbiya", contact: "9106140928" },
    { rank: 19, firstName: "Darshi", lastName: "Barasara", contact: "9879010078" },
    { rank: 20, firstName: "Jaimin", lastName: "Virpariya", contact: "9714942892" },
    { rank: 21, firstName: "Sneh", lastName: "Bhimani", contact: "8200495209" },
    { rank: 22, firstName: "Yash", lastName: "Vora", contact: "7573095853" },
    { rank: 23, firstName: "Gopi", lastName: "Vaghadiya", contact: "7874133370" },
    { rank: 24, firstName: "Nij", lastName: "Kanani", contact: "6354645668" },
    { rank: 25, firstName: "Harshil", lastName: "Parmar", contact: "8799081754" },
    { rank: 26, firstName: "Preksha", lastName: "Kavar", contact: "9824870246" },
    { rank: 27, firstName: "Siddhraj", lastName: "Tamaliya", contact: "8780527133" },
    { rank: 28, firstName: "Vincy", lastName: "Patel", contact: "9328089275" },
    { rank: 29, firstName: "Swara", lastName: "Loriya", contact: "9601275223" },
    { rank: 30, firstName: "Pallavi", lastName: "Bhut", contact: "6353769693" },
    { rank: 31, firstName: "Yogesh", lastName: "Patel", contact: "9924713005" },
    { rank: 32, firstName: "Khodal", lastName: "Padsumbiya", contact: "9925672214" },
    { rank: 33, firstName: "Ansh Vinodbhai", lastName: "Chavda", contact: "8200660604" },
    { rank: 34, firstName: "Heer", lastName: "Padsumbiya", contact: "9638681481" },
    { rank: 35, firstName: "Hetvi Dhavalbhai", lastName: "Godhani", contact: "9998575016" },
    { rank: 36, firstName: "Gyani", lastName: "Padsumbiya", contact: "7567697175" },
    { rank: 37, firstName: "Aayush", lastName: "Khambhala", contact: "9898292943" },
    { rank: 38, firstName: "Hardik", lastName: "Dharodiya", contact: "9870047671" },
    { rank: 39, firstName: "Hemil", lastName: "Gohel", contact: "7016695213" },
    { rank: 40, firstName: "Neej", lastName: "Aghera", contact: "9428622567" }
];

export default function LeaderboardPage() {
    const [visibleCount, setVisibleCount] = useState(10);
    const [loggedInContact, setLoggedInContact] = useState<string | null>(null);
    const canvasRef = useRef<HTMLCanvasElement | null>(null);

    useEffect(() => {
        const studentData = getStudentData();
        if (studentData && studentData.contactNo) {
            setLoggedInContact(studentData.contactNo);
        }
    }, []);

    const drawCertificate = (imageSrc: string, name: string, rank?: number) => {
        const canvas = canvasRef.current;
        if (!canvas) {
            toast.error('Canvas not available');
            return;
        }

        const ctx = canvas.getContext('2d', { alpha: false });
        if (!ctx) {
            toast.error('Failed to get canvas context');
            return;
        }

        const image = document.createElement('img');
        image.src = imageSrc;

        image.onload = async () => {
            try {
                await document.fonts.ready;

                const scaleFactor = 2;
                const mmToPx = 3.78 * scaleFactor;
                const pdfWidthMM = 297;
                const pdfHeightMM = 210;

                const canvasWidth = pdfWidthMM * mmToPx;
                const canvasHeight = pdfHeightMM * mmToPx;

                canvas.width = canvasWidth;
                canvas.height = canvasHeight;
                ctx.scale(scaleFactor, scaleFactor);

                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(image, 0, 0, canvas.width / scaleFactor, canvas.height / scaleFactor);

                ctx.textRendering = 'geometricPrecision';
                ctx.fontKerning = 'normal';
                ctx.textBaseline = 'middle';

                const nameX = 560;
                const nameY = 458;

                ctx.font = '48px Balsamiq Sans';
                ctx.textAlign = 'center';
                ctx.strokeStyle = 'rgba(0, 0, 0, 0.4)';
                ctx.lineWidth = 3;
                ctx.strokeText(name, nameX, nameY);

                ctx.fillStyle = '#FFFFFF';
                ctx.fillText(name, nameX, nameY);

                if (rank !== undefined && rank !== null) {
                    const rankX = 368;
                    const rankY = 503;

                    ctx.font = '22px Balsamiq Sans';
                    ctx.strokeText(rank.toString(), rankX, rankY);
                    ctx.fillText(`#${rank}`, rankX, rankY);
                }

                const imgData = canvas.toDataURL('image/jpeg', 1.0);
                const link = document.createElement('a');
                link.href = imgData;
                link.download = `UWHIZ_Certificate_${name}.jpg`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error('Error generating certificate:', error);
                toast.error('Failed to generate certificate');
            }
        };

        image.onerror = (error: any) => {
            toast.error(error.message || 'Failed to load data');
        };
    };

    const handleCertificateDownload = (firstName: string, lastName: string) => {
        drawCertificate('/uwhizPrticipateCertificate.png', `${firstName} ${lastName}`);
    };

    const handleRankCertificateDownload = (firstName: string, lastName: string, rank: number) => {
        drawCertificate('/uwhizRankCertificate.png', `${firstName} ${lastName}`, rank);
    };

    const topOne = staticData.filter((user) => user.rank === 1);
    const remainingUsers = staticData.filter((user) => user.rank > 1);

    const loggedInUser = loggedInContact ? remainingUsers.find((user) => user.contact === loggedInContact) : null;
    const otherUsers = loggedInUser ? remainingUsers.filter((user) => user.contact !== loggedInContact) : remainingUsers;
    const visibleUsers = loggedInUser ? [loggedInUser, ...otherUsers.slice(0, visibleCount - 1)] : remainingUsers.slice(0, visibleCount);

    const renderProfile = (user: UwhizKidsResult, size: number = 96, isFirst: boolean = false) => {
        const profile = (
            <div
                style={{ width: size, height: size }}
                className="flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-lg sm:text-xl md:text-2xl border-4 border-customOrange"
            >
                {user.firstName.charAt(0) + user.lastName.charAt(0)}
            </div>
        );

        return isFirst ? (
            <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            >
                {profile}
            </motion.div>
        ) : (
            profile
        );
    };

    return (
        <>
            <Header />
            <div className="min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-5 flex justify-center">
                <div className="w-full max-w-5xl space-y-6 sm:space-y-8 pt-8">
                    <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange pb-10">
                        Result - Uwhiz Super Kids
                    </h1>

                    {topOne.map((user) => (
                        <motion.div
                            key={user.rank}
                            initial={{ y: 50, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            className="flex flex-col sm:flex-row justify-center items-center gap-6 mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg"
                        >
                            <div className="flex flex-col items-center">
                                <div className="relative scale-110">
                                    <motion.div
                                        animate={{ scale: [1, 1.1, 1] }}
                                        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                                        className="rounded-full border-4 p-1 border-customOrange"
                                    >
                                        <div className="rounded-full border-4 border-white p-1 bg-white">
                                            <Image
                                                src={winnerPhoto}
                                                alt="Winner"
                                                width={120}
                                                height={120}
                                                className="rounded-full object-cover shadow-xl w-24 h-24 sm:w-32 sm:h-32"
                                            />
                                        </div>
                                    </motion.div>

                                    <Crown className="absolute -top-10 sm:-top-16 left-1/2 -translate-x-1/2 text-customOrange w-8 sm:w-12 h-8 sm:h-12" />

                                    <div className="absolute -bottom-4 left-1/2 -translate-x-1/2 bg-orange-500 text-white font-bold rounded-full w-9 h-9 flex items-center justify-center border-4 border-orange-500">
                                        {user.rank}
                                    </div>
                                </div>

                                <p className="mt-6 text-lg sm:text-xl font-semibold text-center">
                                    {user.firstName} {user.lastName}
                                </p>
                                <p className="text-sm sm:text-lg text-center">
                                    Winning Price: ₹1,00,000
                                </p>

                                {loggedInContact === user.contact && (
                                    <div className="flex items-center gap-4">
                                        <Button
                                            onClick={() =>
                                                handleRankCertificateDownload(user.firstName, user.lastName, user.rank)
                                            }
                                            className="bg-customOrange hover:bg-orange-600 text-white font-semibold px-6 py-2 rounded-full transition-all transform hover:scale-105 text-sm sm:text-base"
                                        >
                                            <PiRanking className="text-xl mr-2" />
                                            Download Result
                                        </Button>

                                        <Button
                                            onClick={() => handleCertificateDownload(user.firstName, user.lastName)}
                                            className="bg-customOrange hover:bg-orange-600 text-white font-semibold px-6 py-2 rounded-full transition-all transform hover:scale-105 text-sm sm:text-base flex items-center gap-2"
                                        >
                                            <PiCertificateFill className="text-xl" />
                                            Download Certificate
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </motion.div>
                    ))}

                    <div className="grid gap-4">
                        {visibleUsers.map((user) => {
                            const isCellular = user.rank >= 2 && user.rank <= 11;
                            const isRb = user.rank >= 12 && user.rank <= 40;
                            const isLoggedInUser = loggedInContact === user.contact;

                            return (
                                <div
                                    key={user.rank}
                                    className={`flex items-center justify-between gap-4 bg-white border border-gray-200 shadow-md rounded-xl p-4 sm:p-6 transition hover:shadow-lg ${
                                        isLoggedInUser ? 'bg-yellow-50 border-orange-500' : ''
                                    }`}
                                >
                                    <div className="flex items-center gap-4 min-w-[200px]">
                                        <div className="w-10 h-10 flex items-center justify-center rounded-full bg-orange-100 text-orange-500 font-bold text-base sm:text-lg shadow-sm">
                                            {user.rank}
                                        </div>
                                        {renderProfile(user, 48)}
                                        <p className="text-lg sm:text-xl font-semibold text-black">
                                            {user.firstName} {user.lastName}
                                        </p>
                                    </div>

                                    <div className="flex items-center gap-3 min-w-[200px] justify-center">
                                        {loggedInContact === user.contact && (
                                            <>
                                                <Button
                                                    onClick={() =>
                                                        handleRankCertificateDownload(user.firstName, user.lastName, user.rank)
                                                    }
                                                    className="bg-customOrange hover:bg-orange-600 text-white font-semibold px-4 py-1 rounded-full transition-all transform hover:scale-105 text-xs sm:text-sm"
                                                >
                                                    <PiRanking className="text-lg mr-2" />
                                                   Download Result
                                                </Button>
                                                <Button
                                                    onClick={() => handleCertificateDownload(user.firstName, user.lastName)}
                                                    className="bg-customOrange hover:bg-orange-600 text-white font-semibold px-4 py-1 rounded-full transition-all transform hover:scale-105 text-xs sm:text-sm flex items-center gap-2"
                                                >
                                                    <PiCertificateFill className="text-lg" />
                                                   Download Certificate
                                                </Button>
                                            </>
                                        )}
                                    </div>

                                    <div className="flex items-center gap-2 min-w-[150px] justify-end">
                                        {(isCellular || isRb) && (
                                            <>
                                                <Image
                                                    src={isCellular ? cellularWord : rbNews}
                                                    alt={isCellular ? 'Cellular Word' : 'RB News'}
                                                    width={24}
                                                    height={24}
                                                    className="object-contain"
                                                />
                                                <span
                                                    className={`text-xs font-medium px-2 py-1 rounded-full ${
                                                        isCellular
                                                            ? 'bg-yellow-100 text-yellow-700'
                                                            : 'bg-blue-100 text-blue-700'
                                                    }`}
                                                >
                                                    Gifted by {isCellular ? 'Cellular Word' : 'RB News'}
                                                </span>
                                            </>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {visibleCount < remainingUsers.length && (
                        <div className="flex justify-center mt-8 mb-10">
                            <Button
                                onClick={() => setVisibleCount((prev) => prev + 10)}
                                className="px-6 py-2 rounded-full bg-customOrange text-white"
                            >
                                Load More
                            </Button>
                        </div>
                    )}
                </div>
            </div>
            <canvas ref={canvasRef} style={{ display: 'none' }} />
            <Footer />
        </>
    );
}