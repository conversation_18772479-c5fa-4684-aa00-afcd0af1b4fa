"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Crown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import Image from "next/image";
import { FaBolt } from "react-icons/fa";
import BadgeDisplay from "@/components/ui/badgedisplay";
import confetti from "canvas-confetti";
import {
  getMockExamLeaderboard,
  storeReaction,
} from "@/services/LeaderboardUserApi";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

const tabs = ["Today", "Weekly", "All time"] as const;
type Tab = (typeof tabs)[number];

type ReactionType = "thumbsup" | "whistle" | "party" | "clap" | "angry" | "thumbsdown";

interface Badge {
  badgeType: string;
  badgeSrc: string;
  badgeAlt: string;
  count?: number;
}
interface LeaderboardUser {
  rank: number;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakCount: number;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  badge: {
    streakCount: number;
    badges: Badge[];
    badgeType: string | null;
    badgeSrc: string | null;
    badgeAlt: string | null;
  } | undefined;
  profilePhoto?: string;
}

interface ReactionCounts {
  thumbsup: number;
  whistle: number;
  party: number;
  clap: number;
  angry: number;
  thumbsdown: number;
}

interface ReactionData {
  reaction: ReactionType;
  counts: ReactionCounts;
}

interface LeaderboardResponse {
  data: LeaderboardUser[];
  total: number;
  reactions: { [key: string]: ReactionData };
}

export default function LeaderboardPage() {
  const [selectedTab, setSelectedTab] = useState<Tab>("Today");
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [reactionData, setReactionData] = useState<{
    [key: string]: ReactionData;
  }>({});
  const [activeReactionMenu, setActiveReactionMenu] = useState<string | null>(
    null
  );
  const nameRefs = useRef<{ [key: string]: HTMLParagraphElement | null }>({});
  const reactionMenuRef = useRef<HTMLDivElement | null>(null);

  let loggedInUserId: string | null = null;
  try {
    const data = localStorage.getItem("student_data");
    loggedInUserId = data ? JSON.parse(data).id : null;
  } catch (error: any) {
    console.error("Error retrieving studentId:", error);
    loggedInUserId = null;
  }

  const getBudgetIcon = (coinEarnings: number): string | null => {
    if (coinEarnings >= 100 && coinEarnings <= 499) {
      return "/scholer.svg";
    } else if (coinEarnings >= 500 && coinEarnings <= 999) {
      return "/Mastermind.svg";
    } else if (coinEarnings >= 1000) {
      return "/Achiever.svg";
    }
    return null;
  };

  const triggerConfetti = (studentId: string) => {
    const nameElement = nameRefs.current[studentId];
    if (nameElement) {
      const rect = nameElement.getBoundingClientRect();
      const x = (rect.left + rect.width / 2) / window.innerWidth;
      const y = (rect.top + rect.height / 2) / window.innerHeight;

      confetti({
        particleCount: 100,
        spread: 70,
        origin: { x, y },
        disableForReducedMotion: true,
        zIndex: 1000,
      });
    }
  };

  const fetchLeaderboard = async (
    timeframe: string,
    page: number
  ): Promise<LeaderboardResponse> => {
    try {
      const response = await getMockExamLeaderboard(timeframe, page, 10);
      return response.data as LeaderboardResponse;
    } catch (err: any) {
      throw new Error("Failed to fetch leaderboard: " + err.message);
    }
  };

  const handleReaction = async (
    studentId: string,
    reactionType: ReactionType,
  ) => {
    if (loggedInUserId && studentId !== loggedInUserId) {

      const current = reactionData[studentId];

      if (current?.reaction === reactionType) return;

      setReactionData((prev) => {
        const prevReaction = prev[studentId]?.reaction;

        const updatedCounts = { ...(prev[studentId]?.counts || {}) };

        if (prevReaction) {
          updatedCounts[prevReaction] = Math.max(
            (updatedCounts[prevReaction] || 1) - 1,
            0
          );
        }

        updatedCounts[reactionType] = (updatedCounts[reactionType] || 0) + 1;

        return {
          ...prev,
          [studentId]: {
            reaction: reactionType,
            counts: updatedCounts,
          },
        };
      });

      try {
        await storeReaction(studentId, reactionType, loggedInUserId);
        triggerConfetti(studentId);
      } catch (error: any) {
        console.error("Failed to save reaction:", error);
        setError("Failed to save reaction: " + error.message);
      }
      setActiveReactionMenu(null);
    } else {
      toast.success('Please Login to Celebrate!');
    }
  };

  const loadMoreRecords = async () => {
    setLoadingMore(true);
    setError(null);

    const nextPage = currentPage + 1;
    const timeframe = selectedTab.toLowerCase().replace(" ", "-");
    try {
      const response = await fetchLeaderboard(timeframe, nextPage);
      setLeaderboard((prevLeaderboard) => [
        ...prevLeaderboard,
        ...response.data,
      ]);
      setReactionData((prev) => ({ ...prev, ...response.reactions }));
      setCurrentPage(nextPage);
    } catch (err: unknown) {
      setError(
        "Failed to load more records: " +
        (err instanceof Error ? err.message : String(err))
      );
    }
    setLoadingMore(false);
  };

  useEffect(() => {
    const fetchInitialLeaderboard = async () => {
      setLoading(true);
      setError(null);
      setCurrentPage(1);

      const timeframe = selectedTab.toLowerCase().replace(" ", "-");
      try {
        const response = await fetchLeaderboard(timeframe, 1);
        setLeaderboard(response.data);
        setTotalRecords(response.total);
        setReactionData(response.reactions);
      } catch (error: any) {
        setError("Failed to fetch leaderboard: " + error.message);
        setLeaderboard([]);
      }
      setLoading(false);
    };

    fetchInitialLeaderboard();
  }, [selectedTab]);

  useEffect(() => {
  const handleClickOutside = (event: MouseEvent) => {
    if (
      reactionMenuRef.current &&
      !reactionMenuRef.current.contains(event.target as Node)
    ) {
      setActiveReactionMenu(null);
    }
  };

  if (activeReactionMenu) {
    document.addEventListener("mousedown", handleClickOutside);
  }

  return () => {
    document.removeEventListener("mousedown", handleClickOutside);
  };
}, [activeReactionMenu]);

  const hasMoreRecords = leaderboard.length < totalRecords;

  const renderProfile = (
    user: LeaderboardUser,
    size: number = 120,
    isFirst: boolean = false
  ) => {
    const initials =
      (user.firstName?.charAt(0) || "") + (user.lastName?.charAt(0) || "");

    const profile = (
      <div
        style={{ width: size, height: size }}
        className="flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-2xl border-4 border-customOrange overflow-hidden"
      >
        {user.profilePhoto && user.profilePhoto.trim() !== "" ? (
          <Image
            src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${user.profilePhoto}`}
            alt={`${user.firstName || ""} ${user.lastName || ""}`}
            width={size}
            height={size}
            className="object-cover rounded-full w-full h-full"
          />
        ) : (
          <span>{initials}</span>
        )}
      </div>
    );

    return isFirst ? (
      <motion.div
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      >
        {profile}
      </motion.div>
    ) : (
      profile
    );
  };

  const renderReactionMenu = (studentId: string) => {
    const reactions: { emoji: string; label: ReactionType; color: string }[] = [
      { emoji: "👍", label: "thumbsup", color: "bg-blue-50 border-blue-200" },
      { emoji: "😙", label: "whistle", color: "bg-green-50 border-green-200" },
      { emoji: "🎉", label: "party", color: "bg-yellow-50 border-yellow-200" },
      { emoji: "👏", label: "clap", color: "bg-red-50 border-red-200" },
      { emoji: "😣", label: "angry", color: "bg-orange-50 border-orange-200" },
      { emoji: "👎", label: "thumbsdown", color: "bg-purple-50 border-purple-200" },
    ];

    return (
      <AnimatePresence>
        <motion.div
          ref={reactionMenuRef}
          initial={{ scale: 0.8, opacity: 0, y: 10 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.8, opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className={cn(
            "absolute bottom-14 z-30 flex bg-white p-2 rounded-lg shadow-lg border border-gray-100 gap-1.5",
            "flex-col right-2 left-auto",          // Mobile: vertical, right-aligned
            "sm:flex-row sm:left-1/2 sm:-translate-x-1/2 sm:right-auto" // Desktop: horizontal, centered
          )}
        >
          {reactions.map((reaction) => (
            <motion.button
              key={reaction.label}
              onClick={() => handleReaction(studentId, reaction.label)}
              aria-label={reaction.label}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center justify-between w-full h-6 rounded-md ${reaction.color} border transition-all duration-200 px-1 sm:w-12 sm:h-7 sm:px-1.5 min-w-[40px]`}
            >
              <span className="text-sm">{reaction.emoji}</span>
              <span className="text-xs font-semibold text-gray-700">
                {reactionData[studentId]?.counts?.[reaction.label] || 0}
              </span>
            </motion.button>
          ))}
        </motion.div>
      </AnimatePresence>
    );
  };

  const topThree = leaderboard.slice(0, 3);
  const others = leaderboard.slice(3);

  return (
    <>
      <Header />
      <div className="min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center">
        <div className="w-full max-w-5xl space-y-6 sm:space-y-8 pt-8">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange">
            Daily Quiz Leaderboard
          </h1>
          <p className="text-center text-sm sm:text-base text-muted-foreground font-medium">
            🎁 Top 3 students get free classmate books everyday!
          </p>

          <div className="flex justify-center gap-4 sm:gap-10 overflow-x-auto">
            {tabs.map((tab) => (
              <Button
                key={tab}
                variant={selectedTab === tab ? "default" : "outline"}
                className={`rounded-full px-4 sm:px-6 py-1 sm:py-2 text-sm sm:text-base font-semibold ${selectedTab === tab
                  ? "text-white"
                  : "border-orange-400 text-orange-400"
                  } whitespace-nowrap`}
                aria-label={`Select ${tab} leaderboard`}
                onClick={() => {
                  setSelectedTab(tab);
                  setCurrentPage(1);
                }}
              >
                {tab} {selectedTab === tab && "(" + totalRecords + ")"}
              </Button>
            ))}
          </div>
          {loading && <p className="text-center text-gray-500">Loading...</p>}
          {error && <p className="text-center text-red-500">{error}</p>}
          {!loading && !error && (
            <div className="flex flex-col sm:flex-row justify-around items-center sm:items-end gap-4 sm:gap-6 mt-6 sm:mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg">
              {topThree.map((user, index) => (
                <motion.div
                  key={user.studentId}
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: index * 0.2 }}
                  className={`flex flex-col items-center mt-10 ${index === 0
                    ? "order-2"
                    : index === 1
                      ? "order-1"
                      : "order-3"
                    } relative`}
                >
                  <div className="relative flex flex-col items-center">
                    <div
                      className={`relative rounded-full border-4 p-2 ${index === 0
                        ? "shadow-2xl scale-110 border-customOrange"
                        : "border-orange-500"
                        }`}
                    >
                      {index === 0 && (
                        <Crown className="absolute -top-6 sm:-top-8 left-1/2 -translate-x-1/2 text-customOrange w-6 sm:w-8 h-6 sm:h-8" />
                      )}
                      {renderProfile(user, 64, index === 0)}
                      <div
                        className={`absolute -bottom-4 sm:-bottom-5 left-1/2 -translate-x-1/2 rounded-full flex items-center justify-center font-bold ${index === 0
                          ? "w-7 h-7 sm:w-9 sm:h-9 bg-orange-500 text-white shadow-lg border-4 border-orange-500"
                          : index === 1
                            ? "w-6 h-6 sm:w-8 sm:h-8 bg-orange-500 text-white shadow border-4 border-orange-500"
                            : "w-5 h-5 sm:w-7 sm:h-7 bg-orange-500 text-white border-4 border-orange-500"
                          }`}
                      >
                        {user.rank}
                      </div>
                    </div>
                    <p
                      ref={(el) => {
                        nameRefs.current[user.studentId] = el;
                      }}
                      className="mt-6 sm:mt-8 font-semibold text-base sm:text-lg text-center relative"
                    >
                      {user.firstName} {user.lastName}
                    </p>
                    <div className="mt-2 w-full flex justify-center">
                      <div className="flex items-center justify-center gap-2 sm:gap-3">
                        {getBudgetIcon(user.coinEarnings) && (
                          <div className="pt-1 sm:pt-2 flex items-center gap-1">
                            <Image
                              src={getBudgetIcon(user.coinEarnings)!}
                              alt="Budget Icon"
                              width={40}
                              height={40}
                              sizes="(max-width: 640px) 40px, 48px"
                              className="h-10 w-10 sm:h-12 sm:w-12 object-contain"
                              loading="lazy"
                            />
                          </div>
                        )}
                        <BadgeDisplay badge={user.badge} />
                      </div>
                    </div>
                    <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-5 mt-2">
                      <div
                        className={`px-3 sm:px-4 py-1 rounded-full border border-orange-300 text-orange-600 font-bold text-xs sm:text-sm ${index === 0
                          ? "animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]"
                          : "bg-green-100"
                          }`}
                      >
                        <div className="flex items-center">
                          <FaBolt /> {user.score}
                        </div>
                      </div>
                      <div
                        className={`px-3 sm:px-4 py-1 rounded-full border border-green-300 text-green-600 font-bold text-xs sm:text-sm flex items-center gap-1 ${index === 0
                          ? "animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]"
                          : "bg-green-100"
                          }`}
                      >
                        <Image
                          src="/uest_coin.png"
                          alt="Coin"
                          width={12}
                          height={12}
                          sizes="(max-width: 640px) 12px, 16px"
                          loading="lazy"
                        />
                        {user.coinEarnings}
                      </div>
                      <div
                        className={`px-3 sm:px-4 py-1 rounded-full border border-blue-300 text-blue-600 font-bold text-xs sm:text-sm ${index === 0
                          ? "animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]"
                          : "bg-blue-100"
                          }`}
                      >
                        🔥 {user.streakCount}
                      </div>
                    </div>
                    {user.studentId !== loggedInUserId && (
                      <div className="mt-3 relative flex justify-center">
                        <Button
                          variant="outline"
                          className="rounded-full px-4 py-1 text-sm border-gray-300 text-gray-600 hover:bg-gray-100 min-w-[80px] sm:min-w-[100px]"
                          onClick={() =>
                            setActiveReactionMenu(
                              activeReactionMenu === user.studentId ? null : user.studentId
                            )
                          }
                        >
                          {(() => {
                            const counts = reactionData[user.studentId]?.counts;
                            if (counts) {
                              const maxReaction = Object.entries(counts).reduce(
                                (max, [reaction, count]) =>
                                  count > max.count ? { reaction, count } : max,
                                { reaction: "", count: -1 }
                              );
                              if (maxReaction.count > 0 && maxReaction.reaction in counts) {
                                const emojis: { [key in ReactionType]: string } = {
                                  thumbsup: "👍",
                                  whistle: "😙",
                                  party: "🎉",
                                  clap: "👏",
                                  angry: "😣",
                                  thumbsdown: "👎",
                                };
                                return (
                                  <span className="flex items-center gap-1 truncate">
                                    {emojis[maxReaction.reaction as ReactionType]}{" "}
                                    {maxReaction.count}
                                  </span>
                                );
                              }
                            }
                            return "Celebrate";
                          })()}
                        </Button>
                        {activeReactionMenu === user.studentId && renderReactionMenu(user.studentId)}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
          {!loading && !error && (
            <div className="rounded-lg mt-6 sm:mt-10 bg-white space-y-3 sm:space-y-4">
              {others.map((user: LeaderboardUser) => (
                <div
                  key={user.studentId}
                  className="flex flex-col sm:flex-row items-center justify-between p-3 sm:p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition"
                >
                  <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto">
                    <div className="relative flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-orange-100 text-orange-500 font-bold text-sm sm:text-lg">
                      {user.rank}
                    </div>
                    {renderProfile(user, 48)}
                    <p
                      ref={(el) => {
                        nameRefs.current[user.studentId] = el;
                      }}
                      className="font-semibold text-base sm:text-lg text-black relative"
                    >
                      {user.firstName} {user.lastName}
                    </p>
                    {user.studentId !== loggedInUserId && (
                      <div className="mt-3 relative">
                        <Button
                          variant="outline"
                          className="rounded-full px-4 py-1 text-sm border-gray-300 text-gray-600 hover:bg-gray-100"
                          onClick={() =>
                            setActiveReactionMenu(
                              activeReactionMenu === user.studentId ? null : user.studentId
                            )
                          }
                        >
                          {(() => {
                            const counts = reactionData[user.studentId]?.counts;
                            if (counts) {
                              const maxReaction = Object.entries(counts).reduce(
                                (max, [reaction, count]) =>
                                  count > max.count ? { reaction, count } : max,
                                { reaction: "", count: -1 }
                              );
                              if (maxReaction.count > 0) {
                                const emojis: { [key in ReactionType]: string } = {
                                  thumbsup: "👍",
                                  whistle: "😙",
                                  party: "🎉",
                                  clap: "👏",
                                  angry: "😣",
                                  thumbsdown: "👎",
                                };
                                return (
                                  <span className="flex items-center gap-1">
                                    {emojis[maxReaction.reaction as ReactionType]}{" "}
                                    {maxReaction.count}
                                  </span>
                                );
                              }
                            }
                            return "Celebrate";
                          })()}
                        </Button>
                        {activeReactionMenu === user.studentId && renderReactionMenu(user.studentId)}
                      </div>
                    )}
                  </div>
                  <div className="flex flex-wrap justify-center items-center gap-3 sm:gap-5 mt-3 sm:mt-0 w-full sm:w-auto">
                    <div className="flex items-center gap-2 sm:gap-3">
                      {getBudgetIcon(user.coinEarnings) && (
                        <div className="pt-1 sm:pt-2 flex items-center gap-1">
                          <Image
                            src={getBudgetIcon(user.coinEarnings)!}
                            alt="Budget Icon"
                            width={40}
                            height={40}
                            sizes="(max-width: 640px) 40px, 48px"
                            className="h-10 w-10 sm:h-12 sm:w-12 object-contain"
                            loading="lazy"
                          />
                        </div>
                      )}
                      <BadgeDisplay badge={user.badge} />
                    </div>
                    <div className="flex items-center gap-2 sm:gap-3 flex-wrap justify-center">
                      <div className="min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-orange-300 bg-orange-100 text-orange-600">
                        <div className="flex items-center">
                          <FaBolt className="mr-1" /> {user.score}
                        </div>
                      </div>
                      <div className="min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-green-300 bg-green-100 text-green-700 flex items-center gap-1">
                        <Image
                          src="/uest_coin.png"
                          alt="Coin"
                          width={12}
                          height={12}
                          sizes="(max-width: 640px) 12px, 16px"
                          loading="lazy"
                        />
                        {user.coinEarnings}
                      </div>
                      <div className="min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-blue-300 bg-blue-100 text-blue-700">
                        🔥 {user.streakCount}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {hasMoreRecords && (
            <div className="flex justify-center mt-6 sm:mt-8">
              <Button
                onClick={loadMoreRecords}
                disabled={loadingMore}
                className="px-6 sm:px-8 py-2 sm:py-3 rounded-full bg-customOrange text-white hover:bg-orange-600 disabled:bg-gray-300 text-sm sm:text-base font-semibold min-w-[120px]"
                aria-label="Load more records"
              >
                {loadingMore ? "Loading..." : "Load More"}
              </Button>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </>
  );
}