import {axiosInstance} from "../lib/axios";

export const saveMockExamResult = async (data: any): Promise<any> => {
  try {
    const response = await axiosInstance.post("/mock-exam-result", data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to save mock exam result: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const getMockExamResults = async (
  studentId: string,
  page: number = 1,
  limit: number = 10,
  filter: { isWeekly?: boolean } = {} 
): Promise<any> => {
  try {
    const query = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(filter.isWeekly !== undefined && { isWeekly: filter.isWeekly.toString() }),
    }).toString();
    const response = await axiosInstance.get(`/mock-exam-result/${studentId}?${query}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam result: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};