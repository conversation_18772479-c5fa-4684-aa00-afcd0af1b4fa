import { axiosInstance } from '@/lib/axios';

export interface Notification {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS' | 'ADMIN';
  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |
        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |
        'STUDENT_STORE_PURCHASE' | 'STUDENT_STORE_ORDER_APPROVED' | 'STUDENT_STORE_ORDER_REJECTED' |
        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |
        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |
        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |
        'CLASS_STORE_PURCHASE' | 'CLASS_STORE_ORDER_APPROVED' | 'CLASS_STORE_ORDER_REJECTED' |
        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' | 'ADMIN_NEW_STORE_ORDER';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: NotificationPagination;
}

// For Classes (authenticated users)
export const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);
  return response.data.data;
};



export const getClassUnreadCount = async (): Promise<number> => {
  const response = await axiosInstance.get('/notifications/classes/count');
  return response.data.data.count;
};

export const markClassNotificationAsRead = async (notificationId: string) => {
  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);
  return response.data;
};

export const markAllClassNotificationsAsRead = async () => {
  const response = await axiosInstance.post('/notifications/classes/mark-all-read');
  return response.data;
};

export const deleteAllClassNotifications = async () => {
  const response = await axiosInstance.delete('/notifications/classes/delete-all');
  return response.data;
};

// For Students (bearer token auth)
export const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);
  return response.data.data;
};



export const getStudentUnreadCount = async (): Promise<number> => {
  const response = await axiosInstance.get('/notifications/students/count');
  return response.data.data.count;
};

export const markStudentNotificationAsRead = async (notificationId: string) => {
  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);
  return response.data;
};

export const markAllStudentNotificationsAsRead = async () => {
  const response = await axiosInstance.post('/notifications/students/mark-all-read');
  return response.data;
};

export const deleteAllStudentNotifications = async () => {
  const response = await axiosInstance.delete('/notifications/students/delete-all');
  return response.data;
};
