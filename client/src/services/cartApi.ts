import { axiosInstance } from '@/lib/axios';

export interface CartItem {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS';
  itemId: string;
  quantity: number;
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    coinPrice: number;
    image: string | null;
    availableStock: number;
  };
}

export interface CartTotal {
  totalCoins: number;
  totalItems: number;
  itemCount: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export const addToCart = async (itemId: string, quantity: number = 1): Promise<ApiResponse<CartItem>> => {
  try {
    const response = await axiosInstance.post('/cart/add', {
      itemId,
      quantity
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to add item to cart'
    };
  }
};

// Get all cart items
export const getCartItems = async (): Promise<ApiResponse<CartItem[]>> => {
  try {
    const response = await axiosInstance.get('/cart');
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to fetch cart items'
    };
  }
};

// Update cart item quantity
export const updateCartItemQuantity = async (itemId: string, quantity: number): Promise<ApiResponse<CartItem>> => {
  try {
    const response = await axiosInstance.put(`/cart/item/${itemId}`, {
      quantity
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to update cart item'
    };
  }
};

// Remove item from cart
export const removeFromCart = async (itemId: string): Promise<ApiResponse<null>> => {
  try {
    const response = await axiosInstance.delete(`/cart/item/${itemId}`);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to remove item from cart'
    };
  }
};

// Clear entire cart
export const clearCart = async (): Promise<ApiResponse<null>> => {
  try {
    const response = await axiosInstance.delete('/cart/clear');
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to clear cart'
    };
  }
};

// Get cart total
export const getCartTotal = async (): Promise<ApiResponse<CartTotal>> => {
  try {
    const response = await axiosInstance.get('/cart/total');
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get cart total'
    };
  }
};
