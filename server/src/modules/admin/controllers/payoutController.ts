import { Request, Response } from "express";
import axios from "axios";
import prisma from "@/config/prismaClient";
import { createNotification } from "@/utils/notifications";
import { NotificationType, UserType } from "@prisma/client";

export const razorpayXClient = axios.create({
  baseURL: "https://api.razorpay.com/v1",
  auth: {
    username: process.env.RAZORPAY_PAYOUT_KEY_ID || "",
    password: process.env.RAZORPAY_PAYOUT_KEY_SECRET || "",
  },
});

export const handlePayout = async (
  req: Request,
  res: Response
): Promise<any> => {
  const {
    classId,
    amount,
    referralId,
  }: { classId: string; amount: number; referralId: string } = req.body;

  try {
    const classes = await prisma.classes.findUnique({
      where: { id: classId },
    });

    const classPayment = await prisma.classesBankPaymentDetails.findUnique({
      where: { classId },
    });

    if (!classPayment) {
      return res
        .status(404)
        .json({ success: false, error: "Class payment details not found" });
    }

    const { defaultMethod, upiId, accountNumber, ifscCode, accountHolderName } =
      classPayment;

    if (!defaultMethod) {
      return res
        .status(400)
        .json({ success: false, error: "Default payment method not set" });
    }

    // 2. Check if Razorpay contact/fund exists in DB
    let payoutMeta = await prisma.razorpayXPayoutMeta.findUnique({
      where: { classId },
    });

    if (!payoutMeta || payoutMeta.defaultMethod !== defaultMethod) {
      // Create new contact if not exists
      const contactRes = await razorpayXClient.post("/contacts", {
        name: accountHolderName,
        type: "vendor",
        contact: classes?.contactNo,
        email: classes?.email,
      });

      const contactId = contactRes.data.id;

      let fundAccountPayload: any = {
        contact_id: contactId,
        account_type: defaultMethod === "BANK" ? "bank_account" : "vpa",
      };

      if (defaultMethod === "BANK") {
        fundAccountPayload.bank_account = {
          name: accountHolderName,
          ifsc: ifscCode,
          account_number: accountNumber,
        };
      } else {
        fundAccountPayload.vpa = {
          address: upiId,
        };
      }

      const fundAccountRes = await razorpayXClient.post(
        "/fund_accounts",
        fundAccountPayload
      );

      // Upsert the payout meta
      payoutMeta = await prisma.razorpayXPayoutMeta.upsert({
        where: { classId },
        update: {
          contactId,
          fundAccountId: fundAccountRes.data.id,
          defaultMethod, // store latest method used
        },
        create: {
          classId,
          contactId,
          fundAccountId: fundAccountRes.data.id,
          defaultMethod,
        },
      });
    }

    const payoutRes = await razorpayXClient.post("/payouts", {
      account_number: process.env.RAZORPAY_PAYOUT_ACCOUNT_NUMBER,
      fund_account_id: payoutMeta.fundAccountId,
      amount: amount * 100, // convert to paise
      currency: "INR",
      mode: defaultMethod === "BANK" ? "IMPS" : "UPI",
      purpose: "payout",
      queue_if_low_balance: true,
    });

    const referral = await prisma.referral.findFirst({
      where: {
        referralLinkId: referralId,
      },
    });

    if (!referral) {
      return res
        .status(400)
        .json({ success: false, error: "Referral not found" });
    }

    await prisma.referralEarning.updateMany({
      where: {
        referralId: referral.id,
      },
      data: {
        paymentStatus: "PAID",
      },
    });

    const title = 'Payment Received for Referral Bonus';
    const message = 'We have credited your payment for the referral bonus. Please check your bank account. The amount should reflect within 2–3 business days.';

    await createNotification({
      userId: classId,
      userType: UserType.CLASS,
      type: NotificationType.CLASS_COIN_PURCHASE,
      title,
      message,
      data: { profileId: classId }
    });

    res.status(200).json({ success: true, payout: payoutRes.data });
  } catch (error: any) {
    console.error("Payout error:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: error.response?.data || error.message,
    });
  }
};

export const handleCoinPayout = async (
  req: Request,
  res: Response
): Promise<any> => {
  const {
    modelId,
    amount,
    referralId,
    modelType,
  }: {
    modelId: string;
    amount: number;
    referralId: string;
    modelType: string;
  } = req.body;

  try {
    await prisma.uestCoins.upsert({
      where: { modelId_modelType: { modelId, modelType } },
      update: { coins: { increment: amount } },
      create: {
        modelId,
        modelType,
        coins: amount,
      },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId,
        modelType,
        type: "CREDIT",
        amount: amount,
        reason: "Coins added for Referral",
      },
    });

    const referral = await prisma.referral.findFirst({
      where: {
        referralLinkId: referralId,
      },
    });

    if (!referral) {
      return res
        .status(400)
        .json({ success: false, error: "Referral not found" });
    }

    await prisma.referralEarning.updateMany({
      where: {
        referralId: referral.id,
      },
      data: {
        paymentStatus: "PAID",
      },
    });


    
    const title = 'Coin Added for Referral Bonus';
    const message = 'We have credited your coint for the referral bonus. Please check your coins.';

    await createNotification({
      userId: modelId,
      userType: modelType === "CLASS" ? UserType.CLASS : UserType.STUDENT,
      type: modelType === "CLASS" ? NotificationType.CLASS_COIN_PURCHASE : NotificationType.STUDENT_COIN_PURCHASE,
      title,
      message,
      data: { profileId: modelId }
    });


    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error("Payout error:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: error.response?.data || error.message,
    });
  }
};
