import { Router } from 'express';
import { purchaseItems, getMyOrders, getOrderDetails } from '../controllers/storePurchaseController';
import jwt from 'jsonwebtoken';

const router = Router();

const combinedAuthMiddleware = async (req: any, res: any, next: any) => {
  const authHeader = req.headers.authorization;
  const hasStudentToken = authHeader && authHeader.startsWith('Bearer ');
  const hasClientToken = req.cookies && req.cookies.client_jwt;

  if (hasStudentToken) {
    try {
      const token = authHeader.split(' ')[1];
      if (token) {
        const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
        const decoded = jwt.verify(token, JWT_SECRET) as { id: string; contactNo: string };

        if (decoded && decoded.id) {
          req.student = {
            id: decoded.id,
            contact: decoded.contactNo ?? ''
          };
          req.authenticatedUser = {
            id: req.student.id,
            userType: 'STUDENT',
            contactNo: req.student.contact
          };
          return next();
        }
      }
    } catch (error) {
      console.error('Student auth failed:', error);
    }
  }

  if (hasClientToken) {
    try {
      const JWT_SECRET = process.env.JWT_SECRET || 'secret123';
      const decoded = jwt.verify(hasClientToken, JWT_SECRET) as { id: string };

      if (decoded && decoded.id) {
        req.class = decoded;
        req.authenticatedUser = {
          id: req.class.id,
          userType: 'CLASS',
          contactNo: req.class.contactNo || req.class.contact || ''
        };
        return next();
      }
    } catch (error) {
      console.error('Client auth failed:', error);
    }
  }

  return res.status(401).json({
    success: false,
    error: 'Login Expired, Please login to continue'
  });
};

router.post('/purchase', combinedAuthMiddleware, purchaseItems);

router.get('/orders', combinedAuthMiddleware, getMyOrders);

router.get('/orders/:orderId', combinedAuthMiddleware, getOrderDetails);

export default router;
