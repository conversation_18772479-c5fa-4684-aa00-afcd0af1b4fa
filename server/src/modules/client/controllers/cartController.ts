import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import * as cartService from '../services/cartService';

interface AuthenticatedRequest extends Request {
  authenticatedUser?: {
    id: string;
    userType: 'STUDENT' | 'CLASS';
    contactNo: string;
  };
}

export const addToCart = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { itemId, quantity = 1 } = req.body;

    if (!itemId) {
      sendError(res, 'Item ID is required', 400);
      return;
    }

    if (quantity <= 0) {
      sendError(res, 'Quantity must be greater than 0', 400);
      return;
    }

    const result = await cartService.addToCart(
      authenticatedUser.id,
      authenticatedUser.userType,
      itemId,
      quantity
    );

    if (!result.success) {
      sendError(res, result.error || 'Failed to add item to cart', 400);
      return;
    }

    sendSuccess(res, result.data, 'Item added to cart successfully');
  } catch (error) {
    console.error('Error in addToCart controller:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const getCartItems = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const result = await cartService.getCartItems(
      authenticatedUser.id,
      authenticatedUser.userType
    );

    if (!result.success) {
      sendError(res, result.error || 'Failed to fetch cart items', 500);
      return;
    }

    sendSuccess(res, result.data, 'Cart items fetched successfully');
  } catch (error) {
    console.error('Error in getCartItems controller:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const updateCartItemQuantity = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { itemId } = req.params;
    const { quantity } = req.body;

    if (!itemId) {
      sendError(res, 'Item ID is required', 400);
      return;
    }

    if (quantity < 0) {
      sendError(res, 'Quantity cannot be negative', 400);
      return;
    }

    const result = await cartService.updateCartItemQuantity(
      authenticatedUser.id,
      authenticatedUser.userType,
      itemId,
      quantity
    );

    if (!result.success) {
      const errorMsg = 'error' in result && result.error ? result.error : 'Failed to update cart item';
      sendError(res, errorMsg, 400);
      return;
    }

    sendSuccess(res, (result as { data?: unknown }).data ?? null, 'Cart item updated successfully');
  } catch (error) {
    console.error('Error in updateCartItemQuantity controller:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const removeFromCart = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { itemId } = req.params;

    if (!itemId) {
      sendError(res, 'Item ID is required', 400);
      return;
    }

    const result = await cartService.removeFromCart(
      authenticatedUser.id,
      authenticatedUser.userType,
      itemId
    );

    if (!result.success) {
      sendError(res, result.error || 'Failed to remove item from cart', 400);
      return;
    }

    sendSuccess(res, null, 'Item removed from cart successfully');
  } catch (error) {
    console.error('Error in removeFromCart controller:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const clearCart = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const result = await cartService.clearCart(
      authenticatedUser.id,
      authenticatedUser.userType
    );

    if (!result.success) {
      sendError(res, result.error || 'Failed to clear cart', 500);
      return;
    }

    sendSuccess(res, null, 'Cart cleared successfully');
  } catch (error) {
    console.error('Error in clearCart controller:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const getCartTotal = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authenticatedUser = req.authenticatedUser;
    if (!authenticatedUser) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const result = await cartService.getCartTotal(
      authenticatedUser.id,
      authenticatedUser.userType
    );

    if (!result.success) {
      sendError(res, result.error || 'Failed to calculate cart total', 500);
      return;
    }

    sendSuccess(res, result.data, 'Cart total calculated successfully');
  } catch (error) {
    console.error('Error in getCartTotal controller:', error);
    sendError(res, 'Internal server error', 500);
  }
};
