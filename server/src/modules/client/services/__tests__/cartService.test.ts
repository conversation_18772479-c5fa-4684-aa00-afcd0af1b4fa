import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ModelType } from '@prisma/client';
import * as cartService from '../cartService';
import prisma from '@/config/prismaClient';

// Mock Prisma client
vi.mock('@/config/prismaClient', () => ({
  default: {
    storeCart: {
      findUnique: vi.fn(),
      delete: vi.fn(),
      update: vi.fn(),
      create: vi.fn(),
      findMany: vi.fn(),
      deleteMany: vi.fn(),
    },
    storeItem: {
      findUnique: vi.fn(),
    },
  },
}));

describe('Cart Service', () => {
  const mockUserId = 'user-123';
  const mockUserType = ModelType.STUDENT;
  const mockItemId = 'item-123';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('removeFromCart', () => {
    it('should successfully remove item from cart when item exists', async () => {
      // Mock existing cart item
      const mockCartItem = {
        id: 'cart-123',
        userId: mockUserId,
        userType: mockUserType,
        itemId: mockItemId,
        quantity: 1,
      };

      (prisma.storeCart.findUnique as any).mockResolvedValue(mockCartItem);
      (prisma.storeCart.delete as any).mockResolvedValue(mockCartItem);

      const result = await cartService.removeFromCart(mockUserId, mockUserType, mockItemId);

      expect(result.success).toBe(true);
      expect(prisma.storeCart.findUnique).toHaveBeenCalledWith({
        where: {
          userId_userType_itemId: {
            userId: mockUserId,
            userType: mockUserType,
            itemId: mockItemId,
          },
        },
      });
      expect(prisma.storeCart.delete).toHaveBeenCalledWith({
        where: {
          userId_userType_itemId: {
            userId: mockUserId,
            userType: mockUserType,
            itemId: mockItemId,
          },
        },
      });
    });

    it('should return error when item does not exist in cart', async () => {
      (prisma.storeCart.findUnique as any).mockResolvedValue(null);

      const result = await cartService.removeFromCart(mockUserId, mockUserType, mockItemId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Item not found in cart');
      expect(prisma.storeCart.delete).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      (prisma.storeCart.findUnique as any).mockRejectedValue(new Error('Database error'));

      const result = await cartService.removeFromCart(mockUserId, mockUserType, mockItemId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to remove item from cart');
    });
  });

  describe('updateCartItemQuantity', () => {
    it('should call removeFromCart when quantity is 0 or negative', async () => {
      const removeFromCartSpy = vi.spyOn(cartService, 'removeFromCart');
      removeFromCartSpy.mockResolvedValue({ success: true });

      await cartService.updateCartItemQuantity(mockUserId, mockUserType, mockItemId, 0);

      expect(removeFromCartSpy).toHaveBeenCalledWith(mockUserId, mockUserType, mockItemId);
    });

    it('should return error when cart item does not exist', async () => {
      (prisma.storeCart.findUnique as any).mockResolvedValue(null);

      const result = await cartService.updateCartItemQuantity(mockUserId, mockUserType, mockItemId, 2);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Item not found in cart');
    });

    it('should successfully update quantity when item exists and stock is sufficient', async () => {
      const mockCartItem = {
        id: 'cart-123',
        userId: mockUserId,
        userType: mockUserType,
        itemId: mockItemId,
        quantity: 1,
      };

      const mockStoreItem = {
        id: mockItemId,
        availableStock: 10,
      };

      const mockUpdatedCartItem = {
        ...mockCartItem,
        quantity: 3,
        item: {
          id: mockItemId,
          name: 'Test Item',
          coinPrice: 100,
          image: null,
          availableStock: 10,
        },
      };

      (prisma.storeCart.findUnique as any).mockResolvedValue(mockCartItem);
      (prisma.storeItem.findUnique as any).mockResolvedValue(mockStoreItem);
      (prisma.storeCart.update as any).mockResolvedValue(mockUpdatedCartItem);

      const result = await cartService.updateCartItemQuantity(mockUserId, mockUserType, mockItemId, 3);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUpdatedCartItem);
    });
  });
});
