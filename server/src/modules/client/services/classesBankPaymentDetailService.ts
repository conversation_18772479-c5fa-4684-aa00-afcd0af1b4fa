import prisma from '@/config/prismaClient';
import { PaymentMethod } from '@prisma/client';

export interface BankPaymentData {
  bankName?: string;
  accountNumber?: string;
  reAccountNumber?: string;
  ifscCode?: string;
  accountHolderName?: string;
  branchName?: string;
  upiId?: string;
  defaultMethod: PaymentMethod;
}

export const createBankPaymentDetails = async (classId: string, data: BankPaymentData) => { 
  const upperCaseData = {
    bankName: data.bankName?.toUpperCase(),
    accountNumber: data.accountNumber?.toUpperCase(),
    reAccountNumber: data.reAccountNumber?.toUpperCase(),
    ifscCode: data.ifscCode?.toUpperCase(),
    accountHolderName: data.accountHolderName?.toUpperCase(),
    branchName: data.branchName?.toUpperCase(),
    upiId: data.upiId?.toLowerCase(),
    defaultMethod: data.defaultMethod,
  };

  return await prisma.classesBankPaymentDetails.create({
    data: {
      classId,
      ...upperCaseData
    }
  });
};

export const updateBankPaymentDetails = async (id: string, data: BankPaymentData) => {
  const upperCaseData = {
     bankName: data.bankName?.toUpperCase(),
    accountNumber: data.accountNumber?.toUpperCase(),
    reAccountNumber: data.reAccountNumber?.toUpperCase(),
    ifscCode: data.ifscCode?.toUpperCase(),
    accountHolderName: data.accountHolderName?.toUpperCase(),
    branchName: data.branchName?.toUpperCase(),
    upiId: data.upiId?.toLowerCase(),
    defaultMethod: data.defaultMethod,
  };

  return await prisma.classesBankPaymentDetails.update({
    where: { id },
    data: {
      ...upperCaseData,
      updatedAt: new Date()
    }
  });
};

export const findBankPaymentByClassId = async (classId: string) => {
  return await prisma.classesBankPaymentDetails.findFirst({
    where: { classId },
    include: {
      class: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          className: true,
          email: true
        }
      }
    }
  });
};

export const findBankPaymentByIdAndClassId = async (id: string, classId: string) => {
  return await prisma.classesBankPaymentDetails.findFirst({
    where: {
      id,
      classId
    }
  });
};

export const deleteBankPaymentById = async (id: string) => {
  return await prisma.classesBankPaymentDetails.delete({
    where: { id }
  });
};

export const getPaymentHistoryByClassId = async (
  classId: string,
  page: number = 1,
  limit: number = 10
) => {
  const skip = (page - 1) * limit;

  const [payments, total] = await Promise.all([
    prisma.classesBankPaymentDetails.findMany({
      where: { classId },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.classesBankPaymentDetails.count({ where: { classId } })
  ]);

  return {
    payments,
    pagination: {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  };
};

export const checkBankPaymentExists = async (classId: string): Promise<boolean> => {
  const count = await prisma.classesBankPaymentDetails.count({
    where: { classId }
  });
  return count > 0;
};
