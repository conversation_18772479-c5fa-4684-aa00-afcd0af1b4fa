<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Admission\Repositories\AdmissionRepository;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class ResolveStudentByToken
{
    protected $admissionRepository;

    public function __construct(AdmissionRepository $admissionRepository)
    {
        $this->admissionRepository = $admissionRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();
        
        if (!$token) {
            return response()->json(apiErrorResonse('Authorization token missing', []), 401);
        }

        try {            
            $decoded = JWT::decode($token, new Key('secret123', 'HS256'));
            $contactNo = $decoded->contactNo;
        } catch (\Exception $e) {
            return response()->json(apiErrorResonse('Invalid token', []), 401);
        }

        $student = $this->admissionRepository->checkByMobile($contactNo);

        if (!$student) {
            return response()->json(apiErrorResonse('Student not found', []), 404);
        }

        $request->merge([
            'student' => $student,
            'student_id' => $student['academicInfo']['id'] ?? null,
            'academic_info' => $student['academicInfo'],
        ]);

        return $next($request);
    }
}
