<?php

namespace App\Http\APIControllers;

use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use StudentLeave\Models\StudentLeave;

class LeaveAPIController extends Controller
{
    protected $studentLeave;

    public function __construct(StudentLeave $studentLeave)
    {
        $this->studentLeave = $studentLeave;
    }

    public function index(Request $request)
    {
        $studentId = $request->get('academic_info')->id;
        $perPage = 10;
        $page = $request->query('page', 1);

        $leaves = $this->studentLeave
            ->where('student_id', $studentId)
            ->orderBy('student_leaves.id', 'DESC')
            ->select('student_leaves.*')
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json(apiResonse("Leave Fetched Successfully", $leaves));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'leave_date' => 'required|date',
            'reason' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $studentId = $request->get('academic_info')->id;
        $data = $request->only(['leave_date', 'reason']);
        $data['student_id'] = $studentId;
        $data['leave_status'] = config('constants.STATUS.PENDING');
        $data['leave_date'] = date('Y-m-d', strtotime($data['leave_date']));

        $existingLeave = $this->studentLeave
            ->where('student_id', $studentId)
            ->where('leave_date', $data['leave_date'])
            ->first();

        if ($existingLeave) {
            return response()->json(apiErrorResonse('Leave exists for this date', []), 422);
        }

        $leaveData = $this->studentLeave::create($data);

        return response()->json(apiResonse("Leave created successfully", $leaveData));
    }

    public function getLeaveById(Request $request, $id)
    {
        $studentId = $request->get('academic_info')->id;
        $leave = $this->studentLeave
            ->where('id', $id)
            ->where('student_id', $studentId)
            ->first();

        if (!$leave) {
            return response()->json(apiErrorResonse('Leave not found', []), 404);
        }

        return response()->json(apiResonse("Leave Fetched successfully", $leave));
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'leave_date' => 'required|date',
            'reason' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(apiErrorResonse('Validation Failed', $validator->errors()), 422);
        }

        $studentId = $request->get('academic_info')->id;
        $data = $request->only(['leave_date', 'reason']);
        $data['leave_date'] = date('Y-m-d', strtotime($data['leave_date']));

        $duplicate = $this->studentLeave
            ->where('student_id', $studentId)
            ->where('id', '!=', $id)
            ->where('leave_date', $data['leave_date'])
            ->first();

        if ($duplicate) {
            return response()->json(apiErrorResonse('Leave exists for this date', []), 422);
        }

        $studentLeave = $this->studentLeave
            ->where('id', $id)
            ->where('student_id', $studentId)
            ->firstOrFail();

        $studentLeave->update($data);

        return response()->json(apiResonse("Leave updated successfully", ""));
    }

    public function destroy(Request $request, $id)
    {
        $studentId = $request->get('academic_info')->id;

        $studentLeave = $this->studentLeave
            ->where('id', $id)
            ->where('student_id', $studentId)
            ->firstOrFail();

        $studentLeave->delete();

        return response()->json(apiResonse("Leave deleted successfully", ""));
    }
}
