<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::model($data, ['id'=>'editdocumentCategory_form','route' => ['documentCategory.update', $data->id]]) !!}
                    @include('DocumentCategory::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('DocumentCategory\Http\Requests\CreateDocumentCategoryRequest', '#editdocumentCategory_form') !!}
<script>
    $("#editdocumentCategory_form").submit(function() {
        event.preventDefault();
        var form = $(this);
        if ($(this).valid()) {
            var url = "{{ route('documentCategory.update',$data->id) }}";
            ajax<PERSON>andler(form, url, 'PATCH', '#editdocumentCategory_form', '#savedocumentCategory', '#newDocumentCategoryEntry', '#documentCategory_table');
            return false;
        }
    });
</script>