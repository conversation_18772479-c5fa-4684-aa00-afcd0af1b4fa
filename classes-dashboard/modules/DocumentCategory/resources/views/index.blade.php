@extends('layouts.app')
@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>DocumentCategorys</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('create documentCategory')
                            <a id="addDocumentCategoryEntry" data-toggle="modal" data-target="#newDocumentCategoryEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New DocumentCategory</a>
                            @endcan
                            @can('export documentCategory data')
                            <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp; Export</button>
                            @endcan
                        </div>
                        <table id="documentCategory_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>DocumentCategory Name</th>
                                    <th>Is optional</th>
                                    <th>Created At</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>DocumentCategory Name</th>
                                    <th>Is optional</th>
                                    <th>Created At</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newDocumentCategoryEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script>
     var documentCategoryRoute = {
        index: "{{ route('documentCategory.index') }}",
        create: "{{ route('documentCategory.create') }}",
        delete: "{{ route('documentCategory.destroy',':did') }}",
        edit: "{{ route('documentCategory.edit',':editid') }}",
        export: "{{ route('export-documentCategory') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/documentCategory/index.js')) }}"></script>
@endsection