<?php

namespace DocumentCategory\Models;

use Document\Models\Document;
use Illuminate\Database\Eloquent\Model;

class DocumentCategories extends Model
{
    protected $table = 'document_categories';

    protected $fillable = [
        'category_name',
        'is_optional',
        'class_uuid'
    ];

    public function documents()
    {
        return $this->hasMany(Document::class, 'category_id');
    }
}
