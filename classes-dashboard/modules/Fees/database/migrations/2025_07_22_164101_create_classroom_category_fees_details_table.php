<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClassroomCategoryFeesDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('classroom_category_fees_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('classroom_fees_details_id')->nullable();
            $table->integer('category_id');
            $table->decimal('amount');
            $table->foreign('classroom_fees_details_id')->references('id')->on('classroom_fees_details')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('installment_fees_details');
    }
}
