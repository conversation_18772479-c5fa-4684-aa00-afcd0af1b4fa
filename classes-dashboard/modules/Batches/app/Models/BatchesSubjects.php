<?php

namespace Batches\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Subject\Models\Subject;

class BatchesSubjects extends Model
{
    use HasFactory;

    public $table = 'batches_subjects';
    protected $fillable = [
        'batch_id',
        'subject_id',
    ];

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }
}
