<?php

namespace Batches\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Timeslots\Models\TimeSlots;

class BatchesTimeslots extends Model
{
    use HasFactory;

    public $table = 'batches_timeslots';
    protected $fillable = [
        'batch_id',
        'timeslot_id',
    ];

    public function timeslot()
    {
        return $this->belongsTo(TimeSlots::class, 'timeslot_id');
    }
}
