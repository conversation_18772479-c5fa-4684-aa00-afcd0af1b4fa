<?php

namespace Batches\Providers;

use <PERSON><PERSON>le\ModulesServiceProvider;

class BatchesServiceProvider extends ModulesServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        parent::register('Batches');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot('Batches');
    }
}
