@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-12 main-title-flex">
                    <h1>Batches</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">

                    <div class="card">
                        <div class="card-body">
                            <div class="generate-buttons">
                                @can('create batches')
                                    <a id="addBatchesEntry" data-toggle="modal" data-target="#newBatchesEntry" href="#"
                                        class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New Batches</a>
                                @endcan
                                @can('export batches data')
                                    <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp;
                                        Export</button>
                                @endcan
                            </div>
                            <table id="batches_table" class="table display  table-striped  table-borderless dt-responsive">
                                <thead>
                                    <tr>
                                        <th>Action</th>
                                        <th>Batch Name</th>
                                        <th>Subjects</th>
                                        <th>Days</th>
                                        <th>Time Slots</th>
                                        <th>Classroom</th>
                                        <th>Resource</th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr class="search-row">
                                        <th>Action</th>
                                        <th>Batch Name</th>
                                        <th>Subjects</th>
                                        <th>Days</th>
                                        <th>Time Slots</th>
                                        <th>Classroom</th>
                                        <th>Resource</th>
                                    </tr>
                                </tfoot>
                            </table>
                            <div class="modal" id="newBatchesEntry" role="dialog" aria-labelledby="roleModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <div id="createContent"></div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('scripts')
    <script>
        var batchesRoute = {
            index: "{{ route('batches.index') }}",
            create: "{{ route('batches.create') }}",
            delete: "{{ route('batches.destroy', ':did') }}",
            edit: "{{ route('batches.edit', ':editid') }}",
            export: "{{ route('export-batches') }}",
        };
    </script>
    <script src="{{ asset(mix('js/page-level-js/batches/index.js')) }}"></script>
@endsection
