$(function () {
    $("#datepicker-timetable").datepicker({
        constrainInput: true,
        showOn: "button",
        dateFormat: "dd-mm-yy",
        buttonText: "btn",
        minDate:new Date(startYearDate),
        maxDate:new Date(endYearDate),
        onSelect: function (dateStr) {
            getData(dateStr);
            $("#fc-dom-1").empty().html(dateStr);
        },
    });
    $(".ui-datepicker-trigger").addClass(
        "fc-today-button fc-button fc-button-primary"
    );
    $(".ui-datepicker-trigger").html(
        '<i class="nav-icon fas fa-calendar-week"></i>'
    );
});


$(document).on("click", ".previosDay", function () {
    previosDay();
});

$(document).on("click", ".nextDay", function () {
    nextDay();
});

$(document).on("click", ".today", function () {
    today();
});

function previosDay() {
    var parse = moment($("#datepicker-timetable").val(), "DD-MM-YYYY").format(
        "MM-DD-YYYY"
    );
    var dates = new Date(parse);
    dates.setDate(dates.getDate() - 7);

    var val = moment(dates, "MM-DD-YYYY").format("DD-MM-YYYY");

    getData(val);
    $("#datepicker-timetable").val(val);
    $("#fc-dom-1").empty().html(val);
}

function nextDay() {
    var parse = moment($("#datepicker-timetable").val(), "DD-MM-YYYY").format(
        "MM-DD-YYYY"
    );
    var dates = new Date(parse);
    dates.setDate(dates.getDate() + 7);

    var val = moment(dates, "MM-DD-YYYY").format("DD-MM-YYYY");

    getData(val);
    $("#datepicker-timetable").val(val);
    $("#fc-dom-1").empty().html(val);
}

function today() {
    var dates = new Date();
    var val = moment(dates, "MM-DD-YYYY").format("DD-MM-YYYY");

    getData(val);
    $("#datepicker-timetable").val(val);
    $("#fc-dom-1").empty().html(val);
}

$("#filtertimetable").on("click", function (event) {
    event.preventDefault();
    getData($("#datepicker-timetable").val());
});

$("#filterreset").click(function () {
    event.preventDefault();
    $("#department_classroom").val("").trigger("change");
    $("#classroom_select").val("").trigger("change");

    getData($("#datepicker-timetable").val());
});

getData($("#datepicker-timetable").val());

$("#department_classroom").change(function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = batchesRoute.getclassroomlist;
    params["requestType"] = `GET`;
    params["data"] = {
        department: $(this).val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(
        response
    ) {
        var select = $(".classroom-data");
        select.empty();
        select.append($('<option value="">All</option>'));
        $.each(response.classlist, function (index, value) {
            select.append(
                $(
                    '<option value="' +
                        value.id +
                        '">' +
                        value.class_name +
                        "</option>"
                )
            );
        });
    };
    commonAjax(params);
});

$(document).on("click", ".exporttimetable", function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = batchesRoute.export;
    params["requestType"] = `GET`;
    params["data"] =
        "classroom=" +
        $("#classroom_select").val() +
        "&department=" +
        $("#department_classroom").val();
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        var url =
        batchesRoute.export +
            "?classroom=" +
            $("#classroom_select").val() +
            "&department=" +
            $("#department_classroom").val();
        window.open(url, "_self");
    };
    commonAjax(params);
});

