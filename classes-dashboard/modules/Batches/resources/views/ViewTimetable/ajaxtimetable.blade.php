@php
    use Carbon\Carbon;

    $weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    $start = Carbon::parse($startdate);
    $dateMap = [];
    foreach ($weekDays as $day) {
        $dateMap[$day] = $start->copy();
        $start->addDay();
    }
@endphp
@foreach ($ctdata->groupBy('batch_id') as $batchId => $entries)
    <h3 class="mt-4 mb-2 font-bold text-lg">{{ $entries->first()->batch_name }}</h3>

    <table class="table table-bordered w-full mb-5">
        <thead>
            <tr>
                <th>Time</th>
                @foreach ($weekDays as $day)
                    <th>
                        {{ $day }}<br>
                        <small>({{ $dateMap[$day]->format('d M Y') }})</small>
                    </th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($entries->groupBy('timeslot_id') as $timeslotId => $slotEntries)
                @php
                    $firstSlot = $slotEntries->first();
                    $isBreak = $firstSlot->is_break === 'Yes';
                @endphp
                <tr>
                    <td>
                       <span class="badge text-center {{ $isBreak ? 'bg-danger text-white' : 'bg-success text-white' }}"> {{ $firstSlot->start_time }} - {{ $firstSlot->end_time }} </span>
                    </td>

                    @foreach ($weekDays as $day)
                        @php
                            $dayData = $slotEntries->firstWhere('day', $day);
                        @endphp
                        <td>
                            @if ($dayData)
                                @if ($dayData->is_break === 'Yes')
                                    <strong>{{ $dayData->break_name }}</strong>
                                @else
                                    @foreach ($dayData->classrooms as $room)
                                        <div class="mb-2 border-b pb-1">
                                            <div><strong>Classroom:</strong> {{ $room['name'] }}</div>
                                            <div><strong>Subjects:</strong>
                                                {{ implode(', ', $room['subjects']->toArray()) }}</div>
                                            @if ($room['resource'])
                                                <div><strong>At:</strong> {{ $room['resource'] }}</div>
                                            @endif
                                            @if ($room['fees'])
                                                <div><strong>Fee:</strong> {{ $room['fees'] }} {{ $room['fee_type'] }}</div>
                                            @endif
                                        </div>
                                    @endforeach
                                @endif
                            @endif
                        </td>
                    @endforeach
                </tr>
            @endforeach
        </tbody>
    </table>
@endforeach
