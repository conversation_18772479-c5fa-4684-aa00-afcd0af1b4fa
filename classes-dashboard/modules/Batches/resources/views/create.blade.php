<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::open(['route' => 'batches.store','id'=>'createbatches_form']) !!}
                    @include('Batches::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('Batches\Http\Requests\CreateBatchesRequest', '#createbatches_form') !!}
<script>
     var createbatchesRoute = {
        store: "{{ route('batches.store') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/batches/create.js')) }}"></script>