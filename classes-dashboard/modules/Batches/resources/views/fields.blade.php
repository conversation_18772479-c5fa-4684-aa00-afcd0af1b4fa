<div class="row">
    {{-- Batch Name --}}
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('batch_name', 'Batch Name *', ['class' => 'form-label']) !!}
            {!! Form::text('batch_name', $data->batch_name ?? null, [
                'class' => 'form-control',
                'placeholder' => 'Enter Batch Name',
            ]) !!}
        </div>
    </div>

    {{-- Classrooms --}}
    <div class="col-md-6">
        <div class="form-group">
            <label>Classrooms *</label>
            <select class="form-control select2" id="classroom" name="classroom[]" multiple>
                @foreach ($classroom as $class)
                    <option value="{{ $class->id }}" @if (isset($data) && collect($data->batchClassroom)->pluck('classroom_id')->contains($class->id)) selected @endif>
                        {{ $class->class_name }} ({{ $class->department_name }})
                    </option>
                @endforeach
            </select>
        </div>
    </div>

    {{-- Subjects (populated via JS based on selected classroom) --}}
    <div class="col-md-6">
        <div class="form-group">
            <label>Subjects *</label>
            <select class="form-control select2" id="subjects-class" name="subjects[]" multiple>
                {{-- Populated via JavaScript --}}
            </select>
        </div>
    </div>

    {{-- Timeslots --}}
    <div class="col-md-12">
        <div class="form-group">
            <label>TimeSlots *</label>
            <select class="form-control select2" name="timeslots[]" multiple>
                @foreach ($timeslots as $ts)
                    <option value="{{ $ts->id }}" @if (isset($data) && collect($data->batchTimeslots)->pluck('timeslot_id')->contains($ts->id)) selected @endif>
                        {{ $ts->start_time }} - {{ $ts->end_time }}
                    </option>
                @endforeach
            </select>
        </div>
    </div>

    {{-- Resource --}}
    <div class="col-md-6">
        <div class="form-group">
            <label>Resource *</label>
            <select class="form-control select2" name="resource">
                <option value="">Select A Resource</option>
                @foreach ($resource as $rc)
                    <option value="{{ $rc->id }}" @if (isset($data) && $data->resource_id == $rc->id) selected @endif>
                        {{ $rc->resource_name }}
                    </option>
                @endforeach
            </select>
        </div>
    </div>

    {{-- Days --}}
    <div class="col-md-6">
        <div class="form-group">
            <label>Days *</label>
            @php
                $selectedDays = isset($data) ? collect($data->days)->pluck('days')->toArray() : [];
            @endphp
            <select class="form-control select2" name="days[]" multiple>
                @foreach (['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                    <option value="{{ $day }}" @if (in_array($day, $selectedDays)) selected @endif>
                        {{ $day }}
                    </option>
                @endforeach
            </select>
        </div>
    </div>

    <div class="col-md-12 mt-3">
        {!! Form::submit(isset($data) ? 'Update' : 'Submit', ['id' => 'savebatches', 'class' => 'btn btn-primary']) !!}
        <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel</button>
    </div>
</div>