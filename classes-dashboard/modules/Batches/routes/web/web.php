<?php

use Batches\Http\Controllers\BatchesController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth'])->group(function () {
    Route::resource('batches', BatchesController::class);
    Route::post('export-batches', [BatchesController::class, 'exportBatches'])->name('export-batches');

    Route::get('view-batch', [BatchesController::class, 'viewBatches'])->name('batches.view');;
    Route::get('export-view-batch', [BatchesController::class, 'exportBatchTimetable'])->name('batches.viewExport');;

});
