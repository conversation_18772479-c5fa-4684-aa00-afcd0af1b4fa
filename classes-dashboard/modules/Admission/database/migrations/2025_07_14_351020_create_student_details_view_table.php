<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateStudentDetailsViewTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("DROP VIEW IF EXISTS student_details_view");

        DB::statement("
    CREATE VIEW student_details_view AS
    SELECT
        sd.id AS id,
        sd.\"firstName\" AS first_name,
        sd.\"lastName\" AS last_name,
        sd.\"middleName\" AS middle_name,
        sd.\"contact\" AS contact_no,
        sd.\"email\" AS email,
        d.name AS department_name,
        d.id AS department_id,
        c.class_name,
        c.id AS classroom_id,
        y.year_name AS year_name,
        y.id AS year_id,
        sai.status,
        sai.class_uuid AS student_class_uuid,
        sai.id AS student_academic_id,
        sp.birthday AS date_of_birth,
        sp.photo AS photo
    FROM
         \"Student\" sd
        INNER JOIN \"StudentProfile\" sp ON sd.id = sp.\"studentId\"
        INNER JOIN student_academic_info sai ON sd.id = sai.student_id
        LEFT JOIN department d ON sai.department = d.id
        LEFT JOIN classrooms c ON sai.classroom = c.id
        LEFT JOIN years y ON sai.year = y.id
");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("DROP VIEW IF EXISTS student_details_view");
    }
}
