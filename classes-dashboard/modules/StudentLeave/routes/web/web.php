<?php

use Illuminate\Support\Facades\Route;
use StudentLeave\Http\Controllers\StudentLeaveController;

Route::middleware(['web', 'auth'])->group(function () {
    // Student Leave Approval
    Route::get('studentleave', [StudentLeaveController::class, 'index'])->name('studentleave.index');
    Route::post('leaveapprove/{leave_id}', [StudentLeaveController::class, 'leave_approve'])->name('studentleave.status');
    Route::post('multipleleaveapprove', [StudentLeaveController::class, 'leave_multipleapprove'])->name('studentleave.multistatus');
    Route::post('export-leave', [StudentLeaveController::class, 'exportLeaves'])->name('export-leaves');
});