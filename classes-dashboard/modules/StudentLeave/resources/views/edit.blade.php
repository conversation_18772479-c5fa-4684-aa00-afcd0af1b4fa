<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::model($data, ['id'=>'editevent_form','route' => ['event.update', $data->id]]) !!}
                    @include('Event::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('Event\Http\Requests\CreateEventRequest', '#editevent_form') !!}
<script>
    $("#editevent_form").submit(function() {
        event.preventDefault();
        var form = $(this);
        if ($(this).valid()) {
            var url = "{{ route('event.update',$data->id) }}";
            ajaxHandler(form, url, 'PATCH', '#editevent_form', '#saveevent', '#newEventEntry', '#event_table');
            return false;
        }
    });
</script>