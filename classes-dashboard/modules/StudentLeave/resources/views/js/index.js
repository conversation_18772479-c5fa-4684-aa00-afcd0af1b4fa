var columns = [
    {
        data: "id",
        name: "student_leaves.id",
        render: function (data, type, full, meta) {
            if (full.leave_status.replace(/<[^>]+>/g, "") == "APPROVED") {
                return (
                    '<div class="custom-control custom-checkbox"><input class="checkbox-m custom-control-input custom-control-input-success" disabled name="checked[]" type="checkbox" id="request-select_' +
                    data +
                    '"><label for="request-select_' +
                    data +
                    '" class="custom-control-label"></label></div>'
                );
            } else {
                return (
                    '<div class="custom-control custom-checkbox"><input class="checkbox-m custom-control-input custom-control-input-success recordselect" value=' +
                    data +
                    ' name="checked[]" type="checkbox" id="request-select_' +
                    data +
                    '"><label for="request-select_' +
                    data +
                    '" class="custom-control-label"></label></div>'
                );
            }
        },
        orderable: false,
    },
    {
        data: "action",
        name: "action",
        orderable: false,
    },
    {
        data: "student_full_name",
        name: "student_details_view.first_name", // Search on first_name
    },
    {
        data: "class_name",
        name: "student_details_view.class_name", // Search on class_name
    },
    {
        data: "leave_date",
        name: "student_leaves.leave_date",
    },
    {
        data: "reason",
        name: "student_leaves.reason",
    },
    {
        data: "leave_status",
        name: "student_leaves.leave_status",
    }
];

var data = function (d) {
    d.start_date = $("#start_date").val();
    d.end_date = $("#end_date").val();
    d.status = $("#status").val();
};

var table = commonDatatable(
    "#studentleave_table",
    leaveRoute.index,
    columns,
    data
);

function tablescroll() {
    $("html, body").animate(
        {
            scrollTop: $("#studentleave_table").offset().top,
        },
        1000
    );
}

const currentYear = new Date().getFullYear();
$("#start_date, #end_date").datepicker({
    dateFormat: "yy-mm-dd",
    changeMonth: true,
    changeYear: true,
    yearRange: `2023:${currentYear}`,
});

$("#filterleaveapptable").on("click", function (event) {
    event.preventDefault();
    tablescroll();
    table.draw();
});

$("#filterreset").click(function () {
    event.preventDefault();
    $("#start_date").val("");
    $("#end_date").val("");
    $("#status").val("").trigger("change");
    tablescroll();
    table.draw();
});

$(document).on("click", ".leavestatus", function () {
    var leaveid = $(this).attr("data-leaveid");
    var url = leaveRoute.status;
    url = url.replace(":leaveid", leaveid);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `POST`;
    params["data"] = {
        subbtn: $(this).val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(data) {
        toastr.success(data.success);
        tablescroll();
        table.draw();
    };

    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$("#studentleave_table").on("draw.dt", function () {
    $(".multipleapproval").prop("disabled", true);
    $(".multipledelete").prop("disabled", true);
    $("#request-select-all").prop("checked", false);
});

$(".multipleapproval").on("click", function () {
    var id = [];

    $(".recordselect:checked").each(function () {
        id.push($(this).val());
    });

    var params = $.extend({}, doAjax_params_default);
    params["url"] = leaveRoute.multipleapprove;
    params["requestType"] = `POST`;
    params["data"] = {
        id: id,
        subbtn: $(this).val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(data) {
        toastr.success(data.success);
        tablescroll();
        table.draw();
        $("#request-select-all").prop("checked", false);
        $(".multipleapproval").prop("disabled", true);
    };

    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).on("click", ".exportData", function () {
    var url = leaveRoute.export;
    var data = {
        start_date: $("#start_date").val(),
        end_date: $("#end_date").val(),
        status: $("#status").val(),
    };

    exportData(url, data);
});
